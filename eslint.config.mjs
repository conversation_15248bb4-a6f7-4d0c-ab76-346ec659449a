import globals from "globals";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default [
  {
    ignores: ["**/*.test.js"],
  },
  ...compat.extends("eslint:recommended"),
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.commonjs,
        ...globals.node,
        process: "readonly",
        Buffer: "readonly",
      },

      ecmaVersion: "latest",
      sourceType: "module",
    },

    rules: {
      "no-unused-vars": "warn",

      "no-console": [
        "warn",
        {
          allow: ["warn", "error"],
        },
      ],

      "no-undef": "error",
      semi: ["error", "always"],
      quotes: ["warn", "double"],

      "no-multiple-empty-lines": [
        "warn",
        {
          max: 2,
        },
      ],

      "no-var": "error",
      "prefer-const": "warn",
      eqeqeq: ["error", "always"],
      "no-trailing-spaces": "warn",
      "comma-dangle": ["warn", "always-multiline"],
    },
  },
];
