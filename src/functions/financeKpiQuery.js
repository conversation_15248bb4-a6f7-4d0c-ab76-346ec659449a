const normalizeToArray = (value) => {
  if (!value) return null;
  return Array.isArray(value) ? value : [value];
};

const buildInClause = (field, values) => {
  if (!values || values.length === 0) return null;
  const escapedValues = values.map((v) => `'${String(v).replace(/'/g, "''")}'`);
  return `${field} IN (${escapedValues.join(", ")})`;
};

const getFinanceKPIRegionwiseQuery = (yearFilter, whereClause, monthFilter, forecastStartMonth, actualMonthRange, selectedMonth) => {
  return `
        WITH cte_vw_dim_admin_property AS (
          Select * from gold_dev.edlh.vw_dim_admin_property
        ),
        cte_vw_income_statement_slicer AS(
          select * from gold_dev.edlh.vw_income_statement_slicer
          where BusinessType='Property Management'
        ),
        cte_FYforecast AS (
          SELECT ExpandTotal, total, c.region,
                 SUM(CASE WHEN MONTH(date) BETWEEN ${forecastStartMonth} AND 12 THEN forecast ELSE 0 END) + 
                 SUM(CASE WHEN MONTH(date) IN (${actualMonthRange}) THEN actual ELSE 0 END) AS forecast,
                 SUM(CASE WHEN YEAR(date)='${yearFilter}' THEN budget ELSE 0 END) AS budget
          FROM gold_dev.edlh.fact_income_statement a
          LEFT JOIN cte_vw_dim_admin_property c ON trim(a.propertyBu) = trim(c.AdminBU) 
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          WHERE expandtotal IN ('TOTAL REVENUE','RETURN ON REVENUE1','TOTAL EXPENSES','RETURN ON REVENUE') 
          AND YEAR(date)='${yearFilter}' AND ${whereClause}
          GROUP BY ALL
        ),
        cte_CYActual AS (
          SELECT ExpandTotal, total, c.region,
                 SUM(Actual) AS actual, 
                 SUM(Budget) AS budget
          FROM gold_dev.edlh.fact_income_statement a
          LEFT JOIN cte_vw_dim_admin_property c ON trim(a.propertyBu) = trim(c.AdminBU)         
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          WHERE expandtotal IN ('TOTAL REVENUE','RETURN ON REVENUE1','TOTAL EXPENSES','RETURN ON REVENUE')
          AND YEAR(date)='${yearFilter}' AND MONTH(date) IN ${monthFilter} AND ${whereClause}
          GROUP BY ALL
        ),
        cte_FYUnits as (
          select typee expandTotal, case when typee='YTD Monthly WA Unit' then 'WAU'
          when typee='YE Monthly WA Unit' then 'WAU_Cal' else typee end total, c.region,
          sum(case when month=12 then budget else 0 end) FY_budget,
          sum(case when month=12 then forecast else 0 end) FY_forecast
          from gold_dev.edlh.fact_property_details_report a
          LEFT JOIN cte_vw_dim_admin_property c ON trim(a.propertyBu) = trim(c.AdminBU)
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          where typee IN ('YE Monthly WA Unit','Units','YTD Monthly WA Unit') AND YEAR(date)='${yearFilter}' AND ${whereClause}
          group by all 
        ),
        cte_CYUnits as (
          select typee expandTotal, case when typee='YTD Monthly WA Unit' then 'WAU'
          when typee='YE Monthly WA Unit' then 'WAU_Cal' else typee end total, c.region,
          sum(actuals) Actual,
          sum(Budget) Budget
          from gold_dev.edlh.fact_property_details_report a
          LEFT JOIN cte_vw_dim_admin_property c ON trim(a.propertyBu) = trim(c.AdminBU)
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          where typee IN ('YE Monthly WA Unit','Units','YTD Monthly WA Unit') and actuals>0 AND YEAR(date)='${yearFilter}' AND MONTH(date) = ${selectedMonth} AND ${whereClause}
          group by all
        ),
        cte_ActualVsBudget AS(
          SELECT a.Region, REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
                 'Total' Total,
                 SUM(actual) AS Actual,
                 SUM(a.budget) AS budget,
                 SUM(actual) - SUM(a.budget) AS variance,
                 try_divide((SUM(actual) - SUM(a.budget)) * 100, SUM(a.budget)) AS Variance_Pert,
                 SUM(forecast) AS forecast,
                 SUM(b.budget) AS budget1,
                 SUM(forecast) - SUM(b.budget) AS Variance1,
                 try_divide((SUM(forecast) - SUM(b.budget)) * 100, SUM(b.budget)) AS FY_Variance_Pert
          FROM cte_CYActual a
          LEFT JOIN cte_FYforecast b ON a.expandtotal = b.expandtotal AND a.total = b.total and a.region = b.region
          GROUP BY ALL
          UNION ALL
          select a.Region, a.expandTotal,
            b.total,
            a.Actual,
            a.Budget,
            a.Actual-a.Budget Variance,
            0 Variance_Pert,
            b.FY_forecast, b.FY_budget, b.FY_forecast-b.FY_budget Variance1,
            0 FY_Variance_Pert
          from cte_CYUnits a
          left join cte_FYUnits b on a.expandTotal = b.expandtotal and a.region = b.region
          UNION ALL
          SELECT a.Region, REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
                 a.Total,
                 SUM(actual) AS Actual,
                 SUM(a.budget) AS budget,
                 SUM(actual) - SUM(a.budget) AS variance,
                 try_divide((SUM(actual) - SUM(a.budget)) * 100, SUM(a.budget)) AS Variance_Pert,
                 SUM(forecast) AS forecast,
                 SUM(b.budget) AS budget1,
                 SUM(forecast) - SUM(b.budget) AS Variance1,
                 try_divide((SUM(forecast) - SUM(b.budget)) * 100, SUM(b.budget)) AS FY_Variance_Pert
          FROM cte_CYActual a
          LEFT JOIN cte_FYforecast b ON a.expandtotal = b.expandtotal AND a.total = b.total and a.region = b.region
          where (a.expandtotal='TOTAL REVENUE' and a.total='Property Management Fees') or a.expandtotal='RETURN ON REVENUE'
          GROUP BY ALL
        ),
        cte_Revenue AS(
          Select Region,'Return on Revenue' FeesType, sum(Actual) SActual,sum(budget) Sbudget, sum(Variance) SVariance,
          sum(Variance_Pert) SVariance_Pert,Sum(forecast) Sforecast,sum(budget1) Sbudget1,
          sum(Variance1) SVariance1,Sum(FY_Variance_Pert) SFY_Variance_Pert
          from cte_ActualVsBudget where Total='Total' and ExpandTotal='RETURN ON REVENUE'
          GROUP BY Region
        ),
        cte_TotalRevenue AS (
          Select * from cte_ActualVsBudget where Total ='Total' and ExpandTotal='TOTAL REVENUE'
        ),
        cte_WAU AS (
          Select Region,'TOTAL REVENUE' ExpandTotal, ifnull(Actual,0) SActual,ifnull(budget,0) Sbudget, ifnull(Variance,0) SVariance,
          ifnull(Variance_Pert,0) SVariance_Pert,ifnull(forecast,0) Sforecast,ifnull(budget1,0) Sbudget1,
          ifnull(Variance1,0) SVariance1,ifnull(FY_Variance_Pert,0) SFY_Variance_Pert
          from cte_ActualVsBudget where Total ='WAU_Cal'
        ),
        cte_WAU_TR AS (
          Select Region,'TOTAL REVENUE' ExpandTotal, ifnull(Actual,0) Actual,ifnull(budget,0) budget, ifnull(Variance,0) Variance,
          ifnull(Variance_Pert,0) Variance_Pert,ifnull(forecast,0) forecast,ifnull(budget1,0) budget1,
          ifnull(Variance1,0) Variance1,ifnull(FY_Variance_Pert,0) FY_Variance_Pert FROM cte_ActualVsBudget
          where ExpandTotal='TOTAL REVENUE' and Total='Total'
        ),
        cte_TotalExpenses AS (
          Select * from cte_ActualVsBudget where Total ='Total' and ExpandTotal='TOTAL EXPENSES'
        ),
        cte_Depreciation AS (
          Select * from cte_ActualVsBudget where Total ='Depreciation/Amortization'
        ),
        cte_CapitalExpenses AS (
          Select * from cte_ActualVsBudget where Total ='Capital Expenditures'
        )
        Select Region,'PM Fees' FeesType, Actual,budget, Variance,
        Variance_Pert,forecast,budget1,Variance1,FY_Variance_Pert
        from cte_ActualVsBudget where Total ='Total' and expandtotal='TOTAL REVENUE' and ifnull(Region,'') !=''
        Union All
        Select b.Region, 'ROR' FeesType, try_divide(b.SActual,a.Actual)*100 Actual,try_divide(b.Sbudget,a.budget)*100 budget,
        (try_divide(b.SActual,a.Actual)*100 - try_divide(b.Sbudget,a.budget)*100) Variance,try_divide(b.SVariance_Pert,a.Variance_pert)*100 Variance_Pert,
        try_divide(b.Sforecast,a.forecast)*100 forecast,try_divide(b.Sbudget1,a.budget1)*100 budget1,
        (try_divide(b.Sforecast,a.forecast)*100 - try_divide(b.Sbudget1,a.budget1)*100) Variance1,try_divide(b.SFY_Variance_Pert,a.FY_Variance_Pert)*100 FY_Variance_Pert
        FROM cte_TotalRevenue a INNER JOIN cte_Revenue b on a.Region=b.region
        Union All
        Select b.Region, 'Fees/Unit' FeesType, try_divide(a.Actual,b.SActual) Actual,try_divide(a.budget,b.Sbudget) budget,
        (try_divide(a.Actual,b.SActual) - try_divide(a.budget,b.Sbudget)) Variance,try_divide(a.Variance_Pert,b.SVariance_pert) Variance_Pert,
        try_divide(a.forecast,b.Sforecast) forecast,try_divide(a.budget1,b.Sbudget1) budget1,
        (try_divide(a.forecast,b.Sforecast) - try_divide(a.budget1,b.Sbudget1)) Variance1,try_divide(a.FY_Variance_Pert,b.SFY_Variance_Pert) FY_Variance_Pert
        FROM cte_WAU_TR a INNER JOIN cte_WAU b on a.Region=b.region
        Union ALl
        Select te.Region,'Cost/Unit' FeesType, try_divide(try_add(try_subtract(coalesce(te.Actual,0),coalesce(dc.Actual,0)),coalesce(ce.Actual,0)),coalesce(wau.SActual,0)) Actual,
        try_divide(try_add(try_subtract(coalesce(te.budget,0),coalesce(dc.budget,0)),coalesce(ce.budget,0)),coalesce(wau.Sbudget,0)) budget,
        (try_divide(try_add(try_subtract(coalesce(te.Actual,0),coalesce(dc.Actual,0)),coalesce(ce.Actual,0)),coalesce(wau.SActual,0)))-(try_divide(try_add(try_subtract(coalesce(te.budget,0),coalesce(dc.budget,0)),coalesce(ce.budget,0)),coalesce(wau.Sbudget,0))) Variance,
        try_divide(try_add(try_subtract(coalesce(te.Variance_Pert,0),coalesce(dc.Variance_Pert,0)),coalesce(ce.Variance_Pert,0)),coalesce(wau.SVariance_Pert,0)) Variance_Pert,
        try_divide(try_add(try_subtract(coalesce(te.forecast,0),coalesce(dc.forecast,0)),coalesce(ce.forecast,0)),coalesce(wau.Sforecast,0)) forecast,
        try_divide(try_add(try_subtract(coalesce(te.budget1,0),coalesce(dc.budget1,0)),coalesce(ce.budget1,0)),coalesce(wau.Sbudget1,0)) budget1,
        (try_divide(try_add(try_subtract(coalesce(te.forecast,0),coalesce(dc.forecast,0)),coalesce(ce.forecast,0)),coalesce(wau.Sforecast,0)))-(try_divide(try_add(try_subtract(coalesce(te.budget1,0),coalesce(dc.budget1,0)),coalesce(ce.budget1,0)),coalesce(wau.Sbudget1,0))) Variance1,
        try_divide(try_add(try_subtract(coalesce(te.FY_Variance_Pert,0),coalesce(dc.FY_Variance_Pert,0)),coalesce(ce.FY_Variance_Pert,0)),coalesce(wau.SFY_Variance_Pert,0)) FY_Variance_Pert
        from cte_TotalExpenses te
        LEFT JOIN cte_WAU wau on te.Region=wau.region
        LEFT JOIN cte_Depreciation dc on te.Region=dc.Region
        LEFT JOIN cte_CapitalExpenses ce on te.Region=ce.Region
        Union All
        Select Region,'WAU' FeesType, Actual,budget, Variance,
        Variance_Pert,forecast,budget1,
        Variance1,FY_Variance_Pert
        from cte_ActualVsBudget where Total ='WAU'
        Union All
        Select Region,'Actual Units' FeesType, Actual,budget, Variance,
        Variance_Pert,forecast,budget1,Variance1,FY_Variance_Pert
        from cte_ActualVsBudget where Total ='Units'
        `;
};

const getFinanceKPIMarketRegionForecastQuery = (yearFilter, whereClause, monthFilter, forecastStartMonth, actualMonthRange, selectedMonth) => {
  return `
        WITH
        cte_vw_dim_admin_property AS (
          Select * from gold_dev.edlh.vw_dim_admin_property
        ),
        cte_vw_income_statement_slicer AS(
          select * from gold_dev.edlh.vw_income_statement_slicer
          where BusinessType='Property Management'
        ),
        cte_FYforecast AS (
          SELECT ExpandTotal,total,c.regionmarket,
                 SUM(CASE WHEN MONTH(date) BETWEEN ${forecastStartMonth} AND 12 THEN forecast ELSE 0 END) +
                 SUM(CASE WHEN MONTH(date) IN (${actualMonthRange}) THEN actual ELSE 0 END) AS forecast,
                 SUM(CASE WHEN YEAR(date)=${yearFilter} THEN budget ELSE 0 END) AS budget
          FROM gold_dev.edlh.fact_income_statement a
          left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          WHERE expandtotal in ('TOTAL REVENUE','RETURN ON REVENUE1','TOTAL EXPENSES','RETURN ON REVENUE')
          AND YEAR(date)=${yearFilter} AND ${whereClause}
          GROUP BY ALL
        ),
        cte_FYUnits as (
          select typee expandTotal, case when typee='YTD Monthly WA Unit' then 'WAU'
          when typee='YE Monthly WA Unit' then 'WAU_Cal' else typee end total,c.regionmarket,
          sum(case when month=12 then budget else 0 end) FY_budget,
          sum(case when month=12 then forecast else 0 end) FY_forecast
          from gold_dev.edlh.fact_property_details_report a
          left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          where typee in ('YE Monthly WA Unit','Units','YTD Monthly WA Unit') AND YEAR(date)=${yearFilter} AND ${whereClause}
          group by all
        ),
        cte_CYUnits as (
          select typee expandTotal, case when typee='YTD Monthly WA Unit' then 'WAU'
          when typee='YE Monthly WA Unit' then 'WAU_Cal' else typee end total,c.regionmarket,
          sum(actuals) Actual,
          sum(Budget) Budget
          from gold_dev.edlh.fact_property_details_report a
          left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          where typee in ('YE Monthly WA Unit','Units','YTD Monthly WA Unit') AND YEAR(date)=${yearFilter}
          AND MONTH(date) = ${selectedMonth} AND ${whereClause}
          group by all
        ),
        cte_ActualVsBudget AS(
          SELECT b.regionmarket,REPLACE(b.ExpandTotal, '1', '') AS ExpandTotal,
                 'Total' Total,
                 SUM(forecast) AS forecast,
                 SUM(b.budget) AS budget,
                 SUM(forecast) - SUM(b.budget) AS Variance,
                 try_divide((SUM(forecast) - SUM(b.budget)) * 100, SUM(b.budget)) AS FY_Variance_Pert
          FROM cte_FYforecast b
          GROUP BY ALL
          union all
          select a.regionmarket,a.expandTotal,
            b.total,
            b.FY_forecast,b.FY_budget,b.FY_forecast-b.FY_budget FY_Varience,
            0 FY_Variance_perct
          from cte_CYUnits a
          left join cte_FYUnits b on a.expandTotal=b.expandtotal and a.regionmarket=b.regionmarket
          union all
          SELECT b.regionmarket,REPLACE(b.ExpandTotal, '1', '') AS ExpandTotal,
                 b.Total,
                 SUM(forecast) AS forecast,
                 SUM(b.budget) AS budget,
                 SUM(forecast) - SUM(b.budget) AS Variance,
                 try_divide((SUM(forecast) - SUM(b.budget)) * 100, SUM(b.budget)) AS FY_Variance_Pert
          FROM cte_FYforecast b
          where (b.expandtotal='TOTAL REVENUE' and b.total='Property Management Fees') or b.expandtotal='RETURN ON REVENUE'
          GROUP BY ALL
        ),
        cte_Revenue AS(
          Select regionmarket,'Return on Revenue' FeesType,Sum(forecast) Sforecast,sum(budget) Sbudget,
          sum(Variance) SVariance,Sum(FY_Variance_Pert) SFY_Variance_Pert
          from cte_ActualVsBudget where Total ='Total' and ExpandTotal='RETURN ON REVENUE'
          GROUP BY regionmarket
        ),
        cte_TotalRevenue AS (
          Select * from cte_ActualVsBudget where Total ='Total' and ExpandTotal='TOTAL REVENUE'
        ),
        cte_WAU AS (
          Select regionmarket,'TOTAL REVENUE' ExpandTotal,ifnull(forecast,0) Sforecast,ifnull(budget,0) Sbudget,
          ifnull(Variance,0) SVariance,ifnull(FY_Variance_Pert,0) SFY_Variance_Pert
          from cte_ActualVsBudget where Total ='WAU_Cal'
        ),
        cte_WAU_TR AS (
          Select regionmarket,'TOTAL REVENUE' ExpandTotal,ifnull(forecast,0) forecast,ifnull(budget,0) budget,
          ifnull(Variance,0) Variance,ifnull(FY_Variance_Pert,0) FY_Variance_Pert FROM cte_ActualVsBudget
          where ExpandTotal='TOTAL REVENUE' and Total='Total'
        ),
        cte_TotalExpenses AS (
          Select * from cte_ActualVsBudget where Total ='Total' and ExpandTotal='TOTAL EXPENSES'
        ),
        cte_Depreciation AS (
          Select * from cte_ActualVsBudget where Total ='Depreciation/Amortization'
        ),
        cte_CapitalExpenses AS (
          Select * from cte_ActualVsBudget where Total ='Capital Expenditures'
        )
        Select * from (
        Select regionmarket,'PM Fees' FeesType,forecast,budget,Variance,FY_Variance_Pert
        from cte_ActualVsBudget where Total ='Total' and expandtotal='TOTAL REVENUE'
        Union All
        Select b.regionmarket, 'ROR' FeesType,try_divide(b.Sforecast,a.forecast)*100 forecast,try_divide(b.Sbudget,a.budget)*100 budget,
        (try_divide(b.Sforecast,a.forecast)*100 - try_divide(b.Sbudget,a.budget)*100) Variance1,try_divide(b.SFY_Variance_Pert,a.FY_Variance_Pert)*100 FY_Variance_Pert
        FROM cte_TotalRevenue a INNER JOIN cte_Revenue b on a.regionmarket=b.regionmarket
        Union All
        Select b.regionmarket, 'Fees/Unit' FeesType,try_divide(a.forecast,b.Sforecast) forecast,try_divide(a.budget,b.Sbudget) budget,
        (try_divide(a.forecast,b.Sforecast) - try_divide(a.budget,b.Sbudget)) Variance,try_divide(a.FY_Variance_Pert,b.SFY_Variance_Pert) FY_Variance_Pert
        FROM cte_WAU_TR a INNER JOIN cte_WAU b on a.regionmarket=b.regionmarket
        Union ALl
        Select te.regionmarket,'Cost/Unit' FeesType,try_divide(try_add(try_subtract(te.forecast,dc.forecast),ce.forecast),wau.Sforecast) forecast,
        try_divide(try_add(try_subtract(te.budget,dc.budget),ce.budget),wau.Sbudget) budget,
        (try_divide(try_add(try_subtract(te.forecast,dc.forecast),ce.forecast),wau.Sforecast))-(try_divide(try_add(try_subtract(te.budget,dc.budget),ce.budget),wau.Sbudget)) Variance,
        try_divide(try_add(try_subtract(te.FY_Variance_Pert,dc.FY_Variance_Pert),ce.FY_Variance_Pert),wau.SFY_Variance_Pert) FY_Variance_Pert
        from cte_TotalExpenses te
        INNER JOIN cte_WAU wau on te.regionmarket=wau.regionmarket
        INNER JOIN cte_Depreciation dc on te.regionmarket=dc.regionmarket
        INNER JOIN cte_CapitalExpenses ce on te.regionmarket=ce.regionmarket
        Union All
        Select regionmarket,'WAU' FeesType,forecast,budget,
        Variance,FY_Variance_Pert
        from cte_ActualVsBudget where Total ='WAU'
        Union All
        Select regionmarket,'Actual Units' FeesType,forecast,budget,Variance,FY_Variance_Pert
        from cte_ActualVsBudget where Total ='Units'
        )
        PIVOT(
                Sum(forecast) Forecast,
                Sum(budget) Budget,
                Sum(Variance) Variance,
                Sum(FY_Variance_Pert) FY_Variance_Pert
                For FeesType in ('PM Fees','ROR','Fees/Unit','Cost/Unit','WAU','Actual Units')
        )
        `;
};

const getFinanceKPIMarketRegionActualQuery = (yearFilter, whereClause, ptdMonthFilter, selectedMonth) => {
  return `
        WITH 
        cte_vw_dim_admin_property AS (
          Select * from gold_dev.edlh.vw_dim_admin_property
        ),
        cte_vw_income_statement_slicer AS(
          select * from gold_dev.edlh.vw_income_statement_slicer
          where BusinessType='Property Management'
        ),
        cte_CYActual AS (
          SELECT ExpandTotal, total, c.regionmarket,
                 SUM(Actual) AS actual, 
                 SUM(Budget) AS budget
          FROM gold_dev.edlh.fact_income_statement a        
          left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)                 
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          WHERE expandtotal in ('TOTAL REVENUE','RETURN ON REVENUE1','TOTAL EXPENSES','RETURN ON REVENUE')
          AND YEAR(date)='${yearFilter}' AND MONTH(date) IN ${ptdMonthFilter} AND ${whereClause}
          GROUP BY ALL
        ),
        cte_FYUnits as (
          select typee expandTotal, case when typee='YTD Monthly WA Unit' then 'WAU'
          when typee='YE Monthly WA Unit' then 'WAU_Cal' else typee end total, c.regionmarket,
          sum(case when month=12 then budget else 0 end) FY_budget,
          sum(case when month=12 then forecast else 0 end) FY_forecast
          from gold_dev.edlh.fact_property_details_report a
          left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          where typee in ('YE Monthly WA Unit','Units','YTD Monthly WA Unit') AND YEAR(date)='${yearFilter}' AND ${whereClause}
          group by all 
        ),
        cte_CYUnits as (
          select typee expandTotal, case when typee='YTD Monthly WA Unit' then 'WAU'
          when typee='YE Monthly WA Unit' then 'WAU_Cal' else typee end total, c.regionmarket,
          sum(actuals) Actual,
          sum(Budget) Budget
          from gold_dev.edlh.fact_property_details_report a
          left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)
          JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
          where typee in ('YE Monthly WA Unit','Units','YTD Monthly WA Unit') AND YEAR(date)='${yearFilter}' 
          AND MONTH(date) IN (${selectedMonth}) AND ${whereClause}
          group by all
        ),
        cte_ActualVsBudget AS(
          SELECT a.regionmarket,REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
                 'Total' Total,
                 SUM(actual) AS Actual,
                 SUM(a.budget) AS budget,
                 SUM(actual) - SUM(a.budget) AS variance,
                 try_divide((SUM(actual) - SUM(a.budget)) * 100, SUM(a.budget)) AS Variance_Pert            
          FROM cte_CYActual a      
          GROUP BY ALL
          union all 
          select a.regionmarket,a.expandTotal,
            b.total,
            a.Actual,
            a.Budget,
            a.Actual-a.Budget Variance,
            0 Variance_Pert    
          from cte_CYUnits a
          left join cte_FYUnits b on a.expandTotal=b.expandtotal and a.regionmarket=b.regionmarket
          union all 
          SELECT a.regionmarket,REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
                 a.Total,
                 SUM(actual) AS Actual,
                 SUM(a.budget) AS budget,
                 SUM(actual) - SUM(a.budget) AS variance,
                 try_divide((SUM(actual) - SUM(a.budget)) * 100, SUM(a.budget)) AS Variance_Pert            
          FROM cte_CYActual a      
          where (a.expandtotal='TOTAL REVENUE' and a.total='Property Management Fees') or a.expandtotal='RETURN ON REVENUE'
          GROUP BY ALL
        ),
        cte_Revenue AS(
          Select regionmarket,'Return on Revenue' FeesType, sum(Actual) SActual,sum(budget) Sbudget, sum(Variance) SVariance,
          sum(Variance_Pert) SVariance_Pert
          from cte_ActualVsBudget where Total ='Total' and ExpandTotal='RETURN ON REVENUE'
          GROUP BY regionmarket
        ),
        cte_TotalRevenue AS (
          Select * from cte_ActualVsBudget where Total ='Total' and ExpandTotal='TOTAL REVENUE'
        ),
        cte_WAU AS (
          Select regionmarket,'TOTAL REVENUE' ExpandTotal, ifnull(Actual,0) SActual,ifnull(budget,0) Sbudget, ifnull(Variance,0) SVariance,
          ifnull(Variance_Pert,0) SVariance_Pert
          from cte_ActualVsBudget where Total ='WAU_Cal'
        ),
        cte_WAU_TR AS (
          Select regionmarket,'TOTAL REVENUE' ExpandTotal, ifnull(Actual,0) Actual,ifnull(budget,0) budget, ifnull(Variance,0) Variance,
          ifnull(Variance_Pert,0) Variance_Pert FROM cte_ActualVsBudget
          where ExpandTotal='TOTAL REVENUE' and Total='Total' 
        ),
        cte_TotalExpenses AS (
          Select * from cte_ActualVsBudget where Total ='Total' and ExpandTotal='TOTAL EXPENSES'
        ),
        cte_Depreciation AS (
          Select * from cte_ActualVsBudget where Total ='Depreciation/Amortization'
        ),
        cte_CapitalExpenses AS (
          Select * from cte_ActualVsBudget where Total ='Capital Expenditures'
        )
        Select * from (
        Select regionmarket,'PM Fees' FeesType, Actual,budget, Variance,Variance_Pert
        from cte_ActualVsBudget where Total ='Total' and expandtotal='TOTAL REVENUE'
        Union All
        Select b.regionmarket, 'ROR' FeesType, try_divide(b.SActual,a.Actual)*100 Actual,try_divide(b.Sbudget,a.budget)*100 budget, 
        (try_divide(b.SActual,a.Actual)*100 - try_divide(b.Sbudget,a.budget)*100) Variance,try_divide(b.SVariance_Pert,a.Variance_pert)*100 Variance_Pert
        FROM cte_TotalRevenue a INNER JOIN cte_Revenue b on a.regionmarket=b.regionmarket
        Union All
        Select b.regionmarket, 'Fees/Unit' FeesType, try_divide(a.Actual,b.SActual) Actual,try_divide(a.budget,b.Sbudget) budget, 
        (try_divide(a.Actual,b.SActual) - try_divide(a.budget,b.Sbudget)) Variance,try_divide(a.Variance_Pert,b.SVariance_pert) Variance_Pert
        FROM cte_WAU_TR a INNER JOIN cte_WAU b on a.regionmarket=b.regionmarket
        Union ALl
        Select te.regionmarket,'Cost/Unit' FeesType, try_divide(try_add(try_subtract(te.Actual,dc.Actual),ce.Actual),wau.SActual) Actual,
        try_divide(try_add(try_subtract(te.budget,dc.budget),ce.budget),wau.Sbudget) budget, 
        (try_divide(try_add(try_subtract(te.Actual,dc.Actual),ce.Actual),wau.SActual))-(try_divide(try_add(try_subtract(te.budget,dc.budget),ce.budget),wau.Sbudget)) Variance,
        try_divide(try_add(try_subtract(te.Variance_Pert,dc.Variance_Pert),ce.Variance_Pert),wau.SVariance_Pert) Variance_Pert
        from cte_TotalExpenses te 
        INNER JOIN cte_WAU wau on te.regionmarket=wau.regionmarket
        INNER JOIN cte_Depreciation dc on te.regionmarket=dc.regionmarket
        INNER JOIN cte_CapitalExpenses ce on te.regionmarket=ce.regionmarket
        Union All
        Select regionmarket,'WAU' FeesType, Actual,budget, Variance,
        Variance_Pert from cte_ActualVsBudget where Total ='WAU'
        Union All 
        Select regionmarket,'Actual Units' FeesType, Actual,budget, Variance,Variance_Pert
        from cte_ActualVsBudget where Total ='Units'
        )
        PIVOT(        
                Sum(Actual) Actual,
                Sum(budget) Budget,
                Sum(Variance) Varriance,
                Sum(Variance_Pert) Variance_Pert      
                For FeesType in ('PM Fees','ROR','Fees/Unit','Cost/Unit','WAU','Actual Units')        
        )
        `;
};

const getFinanceKpiYtdActualVsBudgetQuery = (yearFilter, whereClause, monthFilter, forecastStartMonth, actualMonthRange, selectedMonth) => {
  return `
        WITH 
        cte_vw_dim_admin_property AS (
                Select * from gold_dev.edlh.vw_dim_admin_property
        ),
        cte_vw_income_statement_slicer AS(
                select * from gold_dev.edlh.vw_income_statement_slicer
                where BusinessType='Property Management'
        ),
        cte_FYforecast AS (
                SELECT ExpandTotal,total,
                       SUM(CASE WHEN MONTH(date) BETWEEN ${forecastStartMonth} AND 12 THEN forecast ELSE 0 END) + 
                       SUM(CASE WHEN MONTH(date) IN (${actualMonthRange}) THEN actual ELSE 0 END) AS forecast,
                       SUM(CASE WHEN YEAR(date)='${yearFilter}' THEN budget ELSE 0 END) AS budget
                FROM gold_dev.edlh.fact_income_statement a        
                left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)         
                JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
                WHERE expandtotal in ('TOTAL REVENUE','RETURN ON REVENUE1','TOTAL EXPENSES','RETURN ON REVENUE')
                AND YEAR(date)='${yearFilter}' AND ${whereClause}
                GROUP BY ALL
              ),
        cte_CYActual AS (
                SELECT ExpandTotal,  total,
                       SUM(Actual) AS actual, 
                       SUM(Budget) AS budget
                FROM gold_dev.edlh.fact_income_statement a        
                left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)                 
                JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
                WHERE  expandtotal in ('TOTAL REVENUE','RETURN ON REVENUE1','TOTAL EXPENSES','RETURN ON REVENUE') 
                AND YEAR(date)='${yearFilter}' AND MONTH(date) IN ${monthFilter} AND ${whereClause}
                GROUP BY ALL),

        cte_FYUnits as (
                select  typee  expandTotal, case when typee='YTD Monthly WA Unit' then 'WAU'
        when typee='YE Monthly WA Unit' then 'WAU_Cal' else typee end total,
                sum(case when month=12 then budget else 0 end) FY_budget,
                sum(case when month=12 then forecast else 0 end) FY_forecast
                from gold_dev.edlh.fact_property_details_report a
                left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)
                JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
                where  typee in ('YE Monthly WA Unit','Units','YTD Monthly WA Unit') AND YEAR(date)='${yearFilter}' AND ${whereClause}
        group by all 
        ),
        cte_CYUnits as (
                select typee expandTotal, case when typee='YTD Monthly WA Unit' then 'WAU'
                when typee='YE Monthly WA Unit' then 'WAU_Cal' else typee end total,
                sum(actuals) Actual,
                sum(Budget) Budget
                from gold_dev.edlh.fact_property_details_report a
                left join cte_vw_dim_admin_property c on trim(a.propertyBu) =trim(c.AdminBU)
                JOIN cte_vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
                where typee in ('YE Monthly WA Unit','Units','YTD Monthly WA Unit')  AND YEAR(date)='${yearFilter}'
                AND MONTH(date) IN (${selectedMonth}) AND ${whereClause}
        group by all
        ), 
        cte_ActualVsBudget AS(
        SELECT REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
                     'Total' Total,
                     SUM(actual) AS Actual,
                     SUM(a.budget) AS budget,
                     SUM(actual) - SUM(a.budget) AS variance,
                     try_divide((SUM(actual) - SUM(a.budget)) * 100, SUM(a.budget)) AS Variance_Pert,
                     SUM(forecast) AS forecast,
                     SUM(b.budget) AS budget1,
                     SUM(forecast) - SUM(b.budget) AS Variance1,
                     try_divide((SUM(forecast) - SUM(b.budget)) * 100, SUM(b.budget)) AS FY_Variance_Pert
              FROM cte_CYActual a
              LEFT JOIN cte_FYforecast b ON a.expandtotal = b.expandtotal AND a.total = b.total
              GROUP BY ALL
              union all 
        select a.expandTotal
            ,b.total
            ,a.Actual
            ,a.Budget
            ,a.Actual-a.Budget Varience
            ,0 Variance_perct
            ,b.FY_forecast,b.FY_budget,b.FY_forecast-b.FY_budget FY_Varience
            ,0 FY_Variance_perct 
            from cte_CYUnits a
            left join cte_FYUnits b on a.expandTotal=b.expandtotal
        union all 
        SELECT REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
                     a.Total,
                     SUM(actual) AS Actual,
                     SUM(a.budget) AS budget,
                     SUM(actual) - SUM(a.budget) AS variance,
                     try_divide((SUM(actual) - SUM(a.budget)) * 100, SUM(a.budget)) AS Variance_Pert,
                     SUM(forecast) AS forecast,
                     SUM(b.budget) AS budget1,
                     SUM(forecast) - SUM(b.budget) AS Variance1,
                     try_divide((SUM(forecast) - SUM(b.budget)) * 100, SUM(b.budget)) AS FY_Variance_Pert
              FROM cte_CYActual a
              LEFT JOIN cte_FYforecast b ON a.expandtotal = b.expandtotal AND a.total = b.total
              where (a.expandtotal='TOTAL REVENUE' and a.total='Property Management Fees') or  a.expandtotal='RETURN ON REVENUE'
              GROUP BY ALL
        ),
        cte_Revenue AS(
        Select 'A' UniqueCode ,'Return on Revenue' FeesType, sum(Actual) SActual,sum(budget) Sbudget, sum(Variance) SVariance,
        sum(Variance_Pert) SVariance_Pert,Sum(forecast) Sforecast,sum(budget1) Sbudget1,
        sum(Variance1) SVariance1,Sum(FY_Variance_Pert) SFY_Variance_Pert
        from cte_ActualVsBudget where Total ='Total' and ExpandTotal='RETURN ON REVENUE'
        GROUP BY UniqueCode
        ),
        cte_TotalRevenue AS
        (
        Select 'A' UniqueCode,* from cte_ActualVsBudget where Total ='Total' and ExpandTotal='TOTAL REVENUE'
        ),
        cte_WAU AS
        (
        Select 'A' UniqueCode,'TOTAL REVENUE' ExpandTotal, ifnull(Actual,0) SActual,ifnull(budget,0) Sbudget, ifnull(Variance,0) SVariance,
        ifnull(Variance_Pert,0) SVariance_Pert,ifnull(forecast,0) Sforecast,ifnull(budget1,0) Sbudget1,
        ifnull(Variance1,0) SVariance1,ifnull(FY_Variance_Pert,0) SFY_Variance_Pert
        from cte_ActualVsBudget where Total ='WAU_Cal'
        ),
        cte_WAU_TR AS
        (
            Select 'A' UniqueCode,'TOTAL REVENUE' ExpandTotal, ifnull(Actual,0) Actual,ifnull(budget,0) budget, ifnull(Variance,0) Variance,
            ifnull(Variance_Pert,0) Variance_Pert,ifnull(forecast,0) forecast,ifnull(budget1,0) budget1,
            ifnull(Variance1,0) Variance1,ifnull(FY_Variance_Pert,0) FY_Variance_Pert FROM cte_ActualVsBudget
            where ExpandTotal='TOTAL REVENUE' and Total='Total' 
        ),
        cte_TotalExpenses AS (
                Select 'A' UniqueCode,* from cte_ActualVsBudget where Total ='Total' and ExpandTotal='TOTAL EXPENSES'
        ),
        cte_Depreciation AS (
                Select 'A' UniqueCode,* from cte_ActualVsBudget where Total ='Depreciation/Amortization'
        ),
        cte_CapitalExpenses AS (
                Select 'A' UniqueCode,* from cte_ActualVsBudget where Total ='Capital Expenditures'
        )

        Select 'PM Fees' FeesType, Actual,budget, Variance,
        Variance_Pert,forecast,budget1,Variance1,FY_Variance_Pert
        from cte_ActualVsBudget where Total ='Total' and EXPANDTOTAL = 'TOTAL REVENUE'
        Union All
        Select 'ROR' FeesType, try_divide(b.SActual,a.Actual)*100 Actual,try_divide(b.Sbudget,a.budget)*100 budget, 
        (try_divide(b.SActual,a.Actual)*100 - try_divide(b.Sbudget,a.budget)*100) Variance,try_divide(b.SVariance_Pert,a.Variance_pert)*100 Variance_Pert,
        try_divide(b.Sforecast,a.forecast)*100 forecast,try_divide(b.Sbudget1,a.budget1)*100 budget1,
        (try_divide(b.Sforecast,a.forecast)*100 - try_divide(b.Sbudget1,a.budget1)*100) Variance1,try_divide(b.SFY_Variance_Pert,a.FY_Variance_Pert)*100 FY_Variance_Pert
        FROM cte_TotalRevenue a INNER JOIN cte_Revenue b on a.UniqueCode=b.UniqueCode
        Union All
        Select 'Fees/Unit' FeesType, try_divide(a.Actual,b.SActual) Actual,try_divide(a.budget,b.Sbudget) budget, 
        (try_divide(a.Actual,b.SActual) - try_divide(a.budget,b.Sbudget)) Variance,try_divide(a.Variance_Pert,b.SVariance_pert) Variance_Pert,
        try_divide(a.forecast,b.Sforecast) forecast,try_divide(a.budget1,b.Sbudget1) budget1,
        (try_divide(a.forecast,b.Sforecast) - try_divide(a.budget1,b.Sbudget1)) Variance1,try_divide(a.FY_Variance_Pert,b.SFY_Variance_Pert) FY_Variance_Pert
        FROM cte_WAU_TR a INNER JOIN cte_WAU b on a.UniqueCode=b.UniqueCode
        Union ALl
        Select 'Cost/Unit' FeesType, try_divide(try_add(try_subtract(te.Actual,dc.Actual),ce.Actual),wau.SActual) Actual,
        try_divide(try_add(try_subtract(te.budget,dc.budget),ce.budget),wau.Sbudget) budget, 
        (try_divide(try_add(try_subtract(te.Actual,dc.Actual),ce.Actual),wau.SActual))-(try_divide(try_add(try_subtract(te.budget,dc.budget),ce.budget),wau.Sbudget)) Variance,
        try_divide(try_add(try_subtract(te.Variance_Pert,dc.Variance_Pert),ce.Variance_Pert),wau.SVariance_Pert) Variance_Pert,
        try_divide(try_add(try_subtract(te.forecast,dc.forecast),ce.forecast),wau.Sforecast) forecast,
        try_divide(try_add(try_subtract(te.budget1,dc.budget1),ce.budget1),wau.Sbudget1) budget1,
        (try_divide(try_add(try_subtract(te.forecast,dc.forecast),ce.forecast),wau.Sforecast))-(try_divide(try_add(try_subtract(te.budget1,dc.budget1),ce.budget1),wau.Sbudget1)) Variance1,
        try_divide(try_add(try_subtract(te.FY_Variance_Pert,dc.FY_Variance_Pert),ce.FY_Variance_Pert),wau.SFY_Variance_Pert) FY_Variance_Pert
        from cte_TotalExpenses te 
        INNER JOIN cte_WAU wau on te.UniqueCode=wau.UniqueCode
        INNER JOIN cte_Depreciation dc on te.UniqueCode=dc.UniqueCode
        INNER JOIN cte_CapitalExpenses ce on te.UniqueCode=ce.UniqueCode
        Union All
        Select 'WAU' FeesType, Actual,budget,Variance,
        Variance_Pert,forecast,budget1,
        Variance1,FY_Variance_Pert
        from cte_ActualVsBudget where Total ='WAU'
        Union All 
        Select 'Actual Units' FeesType, Actual,budget, Variance,
        Variance_Pert,forecast,budget1,Variance1,FY_Variance_Pert
        from cte_ActualVsBudget where Total ='Units'
        `;
};

module.exports = {
  normalizeToArray,
  buildInClause,
  getFinanceKPIRegionwiseQuery,
  getFinanceKPIMarketRegionForecastQuery,
  getFinanceKPIMarketRegionActualQuery,
  getFinanceKpiYtdActualVsBudgetQuery,
};
