const { app } = require("@azure/functions");
const { executeDatabricksQuery } = require("./utils/databricks");

app.http("getBusinessDevelopmentKpi", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "business-development-kpi",
  handler: async (request, context) => {
    let query = "";
    try {
      const body = await request.json();

      query = "SELECT * FROM gold_dev.stg.stage_bdkpi";

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getBusinessDevelopmentKpi: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});
