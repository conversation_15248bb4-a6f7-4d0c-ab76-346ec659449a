const { app } = require("@azure/functions");
const { executeDatabricksQuery } = require("./utils/databricks");
const { getUnitWalkKpiMarketQuery, getUnitWalkKpiRegionQuery } = require("./unitWalkKpiQuery");

const normalizeToArray = (value) => {
  if (!value) return null;
  return Array.isArray(value) ? value : [value];
};

const buildInClause = (field, values) => {
  if (!values || values.length === 0) return null;
  const escapedValues = values.map((v) => `'${String(v).replace(/'/g, "''")}'`);
  return `${field} IN (${escapedValues.join(", ")})`;
};

app.http("getUnitWalkMarketKpi", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "unit-walk-kpi-market",
  handler: async (request, context) => {
    let query = "";
    try {
      const {
        year = "2025",
        month = "1",
        department = null,
        businessType = null,
        marketleader = null,
        adminBu = null,
        region = null,
        market = null,
      } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);
      const regions = normalizeToArray(region);
      const markets = normalizeToArray(market);

      // if (years && years.length > 0) {
      //   const yearClause = buildInClause("YEAR(date)", years);
      //   if (yearClause) whereConditions.push(yearClause);
      // }

      // if (months && months.length > 0) {
      //   const monthClause = buildInClause("MONTH(date)", months);
      //   if (monthClause) whereConditions.push(monthClause);
      // }

      if (departments && departments.length > 0) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes && businessTypes.length > 0) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders && marketleaders.length > 0) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus && adminBus.length > 0) {
        const adminBuClause = buildInClause("trim(b.AdminBU)", adminBus);
        if (adminBuClause) whereConditions.push(adminBuClause);
      }

      if (regions && regions.length > 0) {
        const regionClause = buildInClause("b.Region", regions);
        if (regionClause) whereConditions.push(regionClause);
      }

      if (markets && markets.length > 0) {
        const marketClause = buildInClause("RegionMarket", markets);
        if (marketClause) whereConditions.push(marketClause);
      }

      query = getUnitWalkKpiMarketQuery(years, months, whereConditions);

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getUnitWalkMarketKpi: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getUnitWalkRegionKpi", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "unit-walk-kpi-region",
  handler: async (request, context) => {
    let query = "";
    try {
      const {
        year = "2025",
        month = "1",
        department = null,
        businessType = null,
        marketleader = null,
        adminBu = null,
        region = null,
      } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);
      const regions = normalizeToArray(region);

      // if (years && years.length > 0) {
      //   const yearClause = buildInClause("YEAR(date)", years);
      //   if (yearClause) whereConditions.push(yearClause);
      // }

      // if (months && months.length > 0) {
      //   const monthClause = buildInClause("MONTH(date)", months);
      //   if (monthClause) whereConditions.push(monthClause);
      // }

      if (departments && departments.length > 0) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes && businessTypes.length > 0) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders && marketleaders.length > 0) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus && adminBus.length > 0) {
        const adminBuClause = buildInClause("trim(b.AdminBU)", adminBus);
        if (adminBuClause) whereConditions.push(adminBuClause);
      }

      if (regions && regions.length > 0) {
        const regionClause = buildInClause("Region", regions);
        if (regionClause) whereConditions.push(regionClause);
      }

      query = getUnitWalkKpiRegionQuery(years, months, whereConditions);

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getUnitWalkRegionKpi: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});
