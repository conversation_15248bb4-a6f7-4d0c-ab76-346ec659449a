const query = (startDate, endDate, propertyCode) => {
  return `with
dates1 as (select cast('${startDate}' as date) startdate,'${endDate}' as enddate ,'${propertyCode}' property_code ),
dates as(
select cast((select enddate from dates1) as date)-30 t_30
,cast((select enddate from dates1) as date)-30 t_31
, startdate
, enddate  enddate
,add_months(cast((select enddate from dates1) as date),-12) as prevyrenddate
, property_code 
,date_Sub(trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') ,31-
(select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) ) as adjusted_period_start
,case when day(enddate) > (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) then MAKE_DATE(YEAR(enddate), MONTH(enddate), (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1))) else  date_Sub(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)) ,EXTRACT(DAY FROM LAST_DAY(ADD_MONTHS(CAST((select enddate from dates1) as date), -1)))-
(select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) ) end as adjusted_period_end
, cast((select enddate from dates1) as date)-60 t_60
,trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') 1st_day_of_year
,last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)) AS last_day_prev_month
,trunc(CAST((select enddate from dates1) AS DATE), 'MM') AS first_day_of_month  
from dates1
) --select * from dates
,
cte_occu_t30 as (select property_code,unit_code,sstatus
,row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
 from gold_dev.edlh.fact_property_unitscorecard 
 where trim(property_code) in (select property_code from dates)
and dtstart<=(select t_30 from dates)  and trim(unit_status) !='Excluded') ,
cte as (select property_code,unit_code,sstatus
,row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
 from gold_dev.edlh.fact_property_unitscorecard 
 where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded'
and dtstart<=(select enddate from dates)  ) ,
cte_cap_exec as (select month(umonth),year(umonth),sum(smtd*-1)actual,sum(sbudget*-1) budget
,case when sum(smtd*-1) <= sum(sbudget*-1) then sum(smtd*-1) else sum(sbudget*-1) end as actual_budget
 from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates) and cast(umonth as date)>=(select 1st_day_of_year from dates)
and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and left(acct_code,2)=16 
group by month(umonth),year(umonth)
),
cte_vacant as (select property_code,unit_code,sstatus,dtstart,case when dtvacant is null or trim(dtvacant)='' then dtstart else dtvacant end dtvacant
,case when dtend>(select enddate from dates) then (select enddate from dates) else dtend end dtend
,row_number()over(partition by unit_code order by unit_status_hmy desc) rnk1
from gold_dev.edlh.fact_property_unitscorecard where trim(property_code)in(select property_code from dates) and trim(unit_status) !='Excluded'
and dtstart <=(select enddate from dates)  and (dtend>=(select enddate from dates) or dtend is null )),
cte_repeat_tickets as (select  concat(unit_hmy,sub_category,category) cat_hmy,max(dt_Call)dt_Call
from gold_dev.edlh.fact_property_tickets vspt
where  dt_call between (select t_30 from dates) and (select enddate from dates) and date_cancel  is null and unit_hmy >0
and trim(property_code) in(select property_code from dates) group by concat(unit_hmy,sub_category,category)
),
cte_unit_Ava as (
select property_code  ,unit_code ,sStatus ,
row_number()over (partition by unit_hmy order by unit_status_hmy desc) rk
From gold_dev.edlh.fact_property_unitscorecard vspusc where trim(property_code)in (select property_code from dates) 
and trim(unit_status) !='Excluded'
and cast(dtStart as date)<=(select enddate from dates) and trim(unit_status) !='Excluded'
) 
,cte_outstanding as (select  *,TIMESTAMPDIFF(hour,dt_call,case when date_comp is null then (select enddate from dates) else date_comp end) date_diff1
from gold_dev.edlh.fact_property_tickets vspt
where  dt_call between (select t_30 from dates) and (select enddate from dates) and date_cancel  is null 
and trim(property_code) in(select property_code from dates)
)
---------
--collection recovery ratio
select 'Collect recovery ratio',
try_divide(
  (select sum(smtd1*-1) bad_Date_rec
   from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
   and cast(umonth as date)>=(select adjusted_period_start from dates)
   and cast(umonth as date)<=(select adjusted_period_end from dates) and trim(acct_code) in ('404251000','404252000') and ibook=1 ) * -100,
  (select sum(smtd1*-1) bad_date_loss
   from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
   and cast(umonth as date)>=(select adjusted_period_start from dates)
   and cast(umonth as date)<=(select adjusted_period_end from dates) and TRIM(acct_code) in ('404200000','404200050') and ibook=1 )
) as value1
union all 
select 'outstanding tickets',try_divide(sum(case when date_diff1>72 then 1 else 0 end ) * 100,
sum(case when date_diff1 is not null then 1 else 0 end)) value1
 from cte_outstanding

 union all
 select 'total tickets < 5%',sum(case when date_diff1 is not null then 1 else 0 end)*0.05 value1
 from cte_outstanding

 union all 

--- Vacant units 
select 'vcnt units'parameter,count(*) vcnt_units
from cte_vacant where rnk1=1 and sstatus in ('Vacant Unrented Ready', 'Vacant Unrented Not Ready', 'Vacant Rented Not Ready','Vacant Rented Ready')
union all 
select 'avg vcnt days'parameter,avg(days_vacant) from(
select 
  datediff(day,dtvacant,(select enddate from dates)) days_vacant
--select * 
from cte_vacant where rnk1=1 and sstatus like 'Vacant%' ) where days_vacant>30
union all 
select 'aged vacant units'parameter,count(distinct unit_code)cnt 
 from (select unit_code,datediff(day,dtvacant,(select enddate from dates)) days_vacant
from cte_vacant where rnk1=1 and sstatus in('Vacant Unrented Ready', 'Vacant Unrented Not Ready', 'Vacant Rented Not Ready','Vacant Rented Ready'))
where days_vacant>30
union all 
select 'Occupancy_Non_Rev' Parameter,try_divide(((select count(*) occ_non_rev from cte where 
sstatus in ('Occupied No Notice','Notice Unrented','Notice Rented') 
and rnk=1) + (select count(distinct unit_code)  from gold_dev.edlh.fact_property_unitscorecard 
 where trim(property_code)in(select property_code from dates) and trim(sstatus) in ('Admin','Down','Model') 
and (dtEnd>=(select enddate from dates) or dtend is null))) * 100,
(select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard 
 where trim(property_code)in(select property_code from dates)and trim(unit_status) !='Excluded')) Occupancy_Non_Rev
union all 

------------
--Occupancy Trend
select 'Occupancy_Trend' Parameter,try_divide(((select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard 
 where trim(property_code)in (select property_code from dates)and trim(unit_status) !='Excluded')
 -(select count(*) occ_non_rev from cte where  sstatus in  ('Vacant Unrented Ready','Vacant Unrented Not Ready','Notice Unrented')  and rnk=1)) * 100,
 (select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard  
 where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded')) Occupancy_Non_Rev
union all 
---gain loss
select 'gain_loss'parameter 
,sum(case when sevent='Cancel Notice' then cnt else 0 end )
-sum(case when sevent in ('Notice Given', 'Early Termination', 'Skip') then cnt else 0 end)
+sum(case when sevent in ('Submit Application') then cnt else 0 end)
-sum(case when sevent in ('Cancel Move In') then cnt else 0 end )
-sum(case when sevent in ('Application Denied') then cnt else 0 end) gainLoss
--CancelNotices-Notices+SubmitApp-MovinCancel-AppDenail
 from (
select sevent,count(*) cnt
from gold_dev.edlh.fact_prospect where cast(event_logged_Date as date)>=(select startdate from dates) and cast(event_logged_Date as date)<=(select enddate from dates)
and trim(property_scode)in(select property_code from dates)
group by sevent)
union all
------ available units
select 'units available' parameter
,count(distinct unit_code) from cte_unit_Ava 
where rk=1 and sstatus in ('Vacant Unrented Ready', 'Vacant Unrented Not Ready', 'Notice Unrented') 
union all 
--- T30 
select 't30 show' parameter, count(*) T30  from gold_dev.edlh.fact_prospect 
where cast(event_logged_Date as date)>=(select t_31 from dates) and cast(event_logged_Date as date)<=(select enddate from dates)
and trim(property_scode)in(select property_code from dates) and trim(sevent)='Show' and trim(bfirstshow)='-1'
union all 
--In place rent
select 'In_Place_rent' parameter,avg(effective_rent) In_Place_rent
 from gold_dev.edlh.fact_lease_history 
where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
and trim(property_code)in(select property_code from dates) and rnk>=1 ---Note: rnk = Lease no
union all 
select ' In_Place_rent/sqft' parameter,try_divide(sum(effective_rent),sum(dsqft)) Renewal_Place_rent
 from gold_dev.edlh.fact_lease_history 
where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
and trim(property_code)in(select property_code from dates) and rnk>=1
union all
--Previous Year In place rent
select 'Prev_Yr_In_Place_rent' parameter,avg(effective_rent) In_Place_rent
from gold_dev.edlh.fact_lease_history 
where  enddate>=add_months(CAST((select adjusted_period_start from dates) AS DATE), -12) 
and dtleasefrom<=add_months(CAST((select adjusted_period_end from dates) AS DATE), -12)
and trim(property_code)in(select property_code from dates) and rnk>=1
union all 
--New In place rent
select 'New In_Place_rent' parameter,avg(effective_rent) Renewal_Place_rent
 from gold_dev.edlh.fact_lease_history 
where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
and trim(property_code)in(select property_code from dates) and rnk=1
union all 
select 'New In_Place_rent/sqft' parameter,try_divide(sum(effective_rent),sum(dsqft)) Renewal_Place_rent
 from gold_dev.edlh.fact_lease_history 
where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
and trim(property_code)in(select property_code from dates) and rnk=1
Union all
--Previous year New In place rent
select 'Prev_Yr_New In_Place_rent' parameter,avg(effective_rent) Renewal_Place_rent
 from gold_dev.edlh.fact_lease_history 
where enddate>=add_months(CAST((select adjusted_period_start from dates) AS DATE), -12) 
and dtleasefrom<=add_months(CAST((select adjusted_period_end from dates) AS DATE), -12)
and trim(property_code)in(select property_code from dates) and rnk=1
union all 
--renewal In place rent
select 'Renewal In_Place_rent' parameter,avg(effective_rent) Renewal_Place_rent
 from gold_dev.edlh.fact_lease_history 
where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
and trim(property_code)in(select property_code from dates) and rnk>1
union all 
select 'Renewal In_Place_rent/sqft' parameter,try_divide(sum(effective_rent),sum(dsqft)) Renewal_Place_rent
 from gold_dev.edlh.fact_lease_history 
where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
and trim(property_code)in(select property_code from dates) and rnk>1
union all
--previous year renewal In place rent
select 'Prev_Yr_Renewal In_Place_rent' parameter,avg(effective_rent) Renewal_Place_rent
 from gold_dev.edlh.fact_lease_history 
where  enddate>=add_months(CAST((select adjusted_period_start from dates) AS DATE), -12) 
and dtleasefrom<=add_months(CAST((select adjusted_period_end from dates) AS DATE), -12)
and trim(property_code)in(select property_code from dates) and rnk>1
union all 
---MTM
select 'MTM' parameter,
try_divide((select count(distinct unit_code) From   gold_dev.edlh.fact_lease_history 
where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
and trim(property_code)in(select property_code from dates) and dtleaseto<(select enddate from dates)) * 100,
(select count(unit_code) From   gold_dev.edlh.fact_lease_history 
where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
and trim(property_code)in(select property_code from dates)))
union all 
----Income 
select 'Income' parameter,try_divide((sum(smtd1*-1) -sum(sbudget)) * 100, sum(sbudget)) Income from gold_dev.edlh.fact_financial_perf_ytd 
where trim(property_code)in(select property_code from dates)
 and cast(umonth as date)>=(select 1st_day_of_year from dates)
and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(Total_Income)='Total Income'
union all 
----Cont OPex 
select 'Controllable Opex'parameter,try_divide((sum(sbudget)-sum(smtd1*-1)) * 100, sum(sbudget)) Income
  from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
 and cast(umonth as date)>=(select 1st_day_of_year from dates)
and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(finance1)='Controllabe Op Exp'
union all 
----total  OPex 
select 'total OPex' parameter,try_divide((sum(sbudget)-sum(smtd1*-1)) * 100, sum(sbudget)) Income
  from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
 and cast(umonth as date)>=(select 1st_day_of_year from dates)
and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(total_opex)='Total Opex'
union all 
----NOI 
select 'NOI' parameter,try_divide((sum(smtd1*-1)-sum(sbudget)) * 100, sum(sbudget)) Income
  from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
 and cast(umonth as date)>=(select 1st_day_of_year from dates)
and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(NOI)='NOI'
union all 
----Controll NOI 
select 'Controll NOI' parameter,try_divide((sum(smtd1*-1)-sum(sbudget)) * 100, sum(sbudget)) Income
  from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
 and cast(umonth as date)>=(select 1st_day_of_year from dates)
and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(Controllable_NOI)='Controllable NOI'
union all 
------Capital 
select 'Capital' parameter,try_divide((sum(sbudget)-sum(smtd1*-1)) * 100, sum(sbudget)) Income
  from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
 and cast(umonth as date)>=(select 1st_day_of_year from dates)
and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(finance1)='Capital'
union all 
--- cost per turn 
select 'COST Per Turn' parameter,try_divide(sum(smtd1),
(select count(distinct tenant_code) from gold_dev.edlh.fact_lease_history
where dtmoveout>=(select adjusted_period_start from dates) 
and dtmoveout<=(select adjusted_period_end from dates) and trim(property_code)in(select property_code from dates)))
 From gold_dev.edlh.fact_financial_perf_ytd 
where cost='Cost Per Turn' and 
trim(property_code)in(select property_code from dates)
 and cast(umonth as date)>=(select adjusted_period_start from dates)
and cast(umonth as date)<=(select adjusted_period_end from dates) and ibook=1

union all 
---collection MTD 
select 'collection MTD' Parameter,try_divide((
select sum(receipt_amount) 
from gold_dev.edlh.fact_property_transactions where receipt_date<=(select enddate from dates) 
and trim(property_code)in(select property_code from dates)
and charge_date=(select first_day_of_month  from dates) ) * 100,
(select sum(charge_amount) from (select distinct charge_key,charge_amount 
from gold_dev.edlh.fact_property_transactions where charge_date=(select first_day_of_month  from dates) 
and trim(property_code)in(select property_code from dates)))
) mtd
union all 
-----capital execution
 select 'capital execution' Parameter, try_divide(sum(actual) * 100, sum(budget)) cap_exe from cte_cap_exec
 union all 
 ---- avf turn time 
 select --property_code ,unit_code ,dtready  ,dtmoveout ,
'avg_turn_time'parameter,avg(DATEDIFF(day,dtmoveout,dtready)) avg_turn_time
From gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in(select property_code from dates) and trim(unit_status) !='Excluded'
and dtready >=(select t_30 from dates) and dtready<=(select enddate from dates) and dtmoveout is not null 
union all 
-----repeat tickets 
select 'repeat_tickets'parameter,count(*) repeat_tickets from(
select a.unit_hmy ,concat(sub_category,category),count(*)
from gold_dev.edlh.fact_property_tickets a
join cte_repeat_tickets cte on concat(a.unit_hmy,a.sub_category,a.category) =cte.cat_hmy
and a.dt_call between cast(cte.dt_call as date)-30 and cte.dt_Call
where
a.dt_call between (select t_60 from dates) and (select enddate from dates) and date_cancel  is null
and trim(property_code) in(select property_code from dates)
group by a.unit_hmy ,concat(sub_category,category) having count(*)>1
)
union all 
select 'Residential sqft',sum(unit_sqft) residential_sqft from(select distinct unit_code,unit_sqft
 from gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in (select property_code from dates)
 and trim(property_code) not like '%r' and trim(unit_status) !='Excluded')
 union  all
 select 'AVG Residential sqft',avg(unit_sqft) avg_residential_sqft from(select distinct unit_code,unit_sqft
 from gold_dev.edlh.fact_property_unitscorecard 
 where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded' )
 union all 
 select 'Retail sqft',sum(unit_sqft)Retail_sqft from(select distinct unit_code,unit_sqft
 from gold_dev.edlh.fact_property_unitscorecard 
 where trim(property_code) in (select concat(trim(property_code),'r') from dates) and trim(unit_status) !='Excluded')
 union all 
 select 'Retail Spaces',count(distinct unit_code)Retail_spaces from(select distinct unit_code,unit_sqft
 from gold_dev.edlh.fact_property_unitscorecard 
 where trim(property_code) in (select concat(trim(property_code),'r') from dates) and trim(unit_status) !='Excluded' )
 union all 
 select 'Affordable' ,count(distinct unit_code) affordable
 from gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in (select property_code from dates)
 and trim(unit_status) !='Excluded' and sfield8='Y'
 union all 
 select 'Non Revenue' ,count(distinct unit_code) non_rev
 from gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in (select property_code from dates)
 and trim(unit_status) in ('Admin','Model','Down') and 
 (dtend is null or dtend >=(select enddate from dates))
  union all 
 select 'Down' ,count(distinct unit_code) down_units
 from gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in (select property_code from dates) and trim(unit_status) in ('Down') and 
 (dtend is null or dtend >=(select enddate from dates))
 union all 
 select 'Occupancy_Trendt30' Parameter,try_divide(((select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard 
 where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded')-(select count(*) occ_non_rev from cte_occu_t30 
 where  sstatus in  ('Vacant Unrented Ready','Vacant Unrented Not Ready','Notice Unrented')  and rnk=1)) * 100,
 (select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard  where trim(property_code) in (select property_code from dates)
 and trim(unit_status) !='Excluded')) Occupancy_Trendt30

 union all 

 select 'YTD Renewal Conversion' ,try_divide((select count(*) from gold_dev.edlh.fact_lease_history where trim(property_code) in  (select property_code from dates)
 and COALESCE(earlytermination,dtleaseto)>=(select adjusted_period_start from dates)  
 and COALESCE(earlytermination,dtleaseto)<=(select adjusted_period_end from dates) 
 and trim(leaseexpirationresult)='Lease Renewed') * 100,
 (select count(*) from gold_dev.edlh.fact_lease_history 
 where trim(property_code) in (select property_code from dates) 
 and COALESCE(earlytermination,dtleaseto)>=(select adjusted_period_start from dates)
 and COALESCE(earlytermination,dtleaseto)<=(select adjusted_period_end from dates) )) YTD_Renewal_Conv

 union all

 select 'bad debt w/o % GRI',try_divide((select sum(smtd1*-1) from gold_dev.edlh.fact_financial_perf_ytd where ibook=1 
 and trim(acct_code) in ('404200000','404200050')
 and umonth>=(select adjusted_period_start from dates) 
 and umonth<=(select adjusted_period_end from dates)
 AND TRIM(property_code) in (select property_code from dates)) * 100,
 (select sum(smtd1*-1) from gold_dev.edlh.fact_financial_perf_ytd 
 where ibook=1 and GRI='Y'and umonth>=(select adjusted_period_start from dates)  
 and umonth<=(select adjusted_period_end from dates) AND TRIM(property_code) in (select property_code from dates)))  bad_debt_wo
 Union all
 Select 'Adjusted_Period_Start_Month', Month(adjusted_period_start) aMonth from dates
 where property_code=(select property_code from dates)
 Union all
 Select 'Adjusted_Period_Start_Day', Day(adjusted_period_start) aMonth from dates
 where property_code=(select property_code from dates)
 Union all
  Select 'Adjusted_Period_Start_Year', Year(adjusted_period_start) aMonth from dates
 where property_code=(select property_code from dates)
 union all
 Select 'Adjusted_Period_End_Month', Month(adjusted_period_end) amonth from dates
 where property_code=(select property_code from dates)
 union all
 Select 'Adjusted_Period_End_Day', Day(adjusted_period_end) amonth from dates
 where property_code=(select property_code from dates)
 union all
 Select 'Adjusted_Period_End_Year', Year(adjusted_period_end) amonth from dates
 where property_code=(select property_code from dates)
 union all
 select 'Rental Income' parameter,(sum(smtd1*-1) -sum(sbudget))*100/sum(sbudget) Rental_Income
from gold_dev.edlh.fact_financial_perf_ytd
where trim(property_code)in(select property_code from dates)
 and cast(umonth as date)>=(select 1st_day_of_year from dates)
and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and (acct_code like '401%'
or acct_code like '402%' or acct_code like '403%'  or acct_code like '404%' or acct_code like '405%'
or acct_code like '40653%' or acct_code like '40654%' or acct_code like '40655%')
`;
};

const getImagesQuery = (bu) => {
  let query = `
    select b.BU,b.Property,a.name,a.placeid,a.image_url
    from silver_dev.google_api.location_images a
    left join (select * from gold_dev.edlh.dim_property where is_active=true)b on trim(a.placeId)=trim(b.google_place_id)
    where 1=1
  `;

  if (bu) {
    query += ` and b.BU = '${bu}'`;
  }

  return query;
};

const getPropertyStrategyQuery = (startDate, endDate, propertyCode) => {
  return `
    with dates1 as (
      select cast('${startDate}' as date) startdate,
             '${endDate}' as enddate,
             '${propertyCode}' property_code
    ),
    dates as(
      select (select cast(BeginningOfOperations as date)BeginningOfOperations 
              from gold_dev.edlh.dim_property 
              where is_active=true and bu in (select property_code from dates1)) BeginningOfOperations,
             enddate,
             startdate,
             property_Code,
             DATEADD(YEAR, -1, enddate) last_year_enddate,
             timestampdiff(Year,(select BeginningOfOperations 
                                from gold_dev.edlh.dim_property 
                                where is_active=true and bu in (select property_code from dates1)),enddate) year_diff,
             timestampdiff(month,(select BeginningOfOperations 
                                 from gold_dev.edlh.dim_property 
                                 where is_active=true and bu in (select property_code from dates1)),enddate) month_diff
      from dates1
    ),
    cte as (
      select property_code,unit_code,sstatus,
             row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
      from gold_dev.edlh.fact_property_unitscorecard 
      where trim(property_code)=(select property_code from dates)
        and dtstart<=(select enddate from dates)
    ),
    cte_last_year as (
      select property_code,unit_code,sstatus,
             row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
      from gold_dev.edlh.fact_property_unitscorecard 
      where trim(property_code)=(select property_code from dates)
        and dtstart<=(select last_year_enddate from dates)
    )
    select Property_Strategy_This_Year,
           case when Property_Strategy_This_Year= Property_Strategy_Last_Year 
                then 'Same Store' 
                else 'Not Same Store' 
           end Same_Store
    from (
      select 'Property_Strategy_This_Year' Parameter, 
             case when Lifetime_Occupancy_Trend<95 and year_diff<=3 then 'Lease Up'
                  when month_diff<=6 and Lifetime_Occupancy_Trend>80 then 'Take Over' 
                  else 'Stabilised' 
             end Property_Strategy  
      from(
        select 'Lifetime_Occupancy_Trend' Parameter,
               try_divide(((select count(distinct unit_code) 
                 from gold_dev.edlh.fact_property_unitscorecard  
                 where trim(property_code)=(select property_code from dates))
                -(select count(*) occ_non_rev 
                  from cte 
                  where sstatus in ('Vacant Unrented Ready','Vacant Unrented Not Ready','Notice Unrented')  
                    and rnk=1))*100,(select count(distinct unit_code) 
                                     from gold_dev.edlh.fact_property_unitscorecard  
                                     where trim(property_code)=(select property_code from dates))) Lifetime_Occupancy_Trend,
               year_diff,month_diff 
        from dates
      )
      union all 
      select 'Property_Strategy_Last_Year' Parameter, 
             case when Lifetime_Occupancy_Trend<95 and year_diff between 1 and 3 then 'Lease Up'
                  when month_diff between 1 and 6 and Lifetime_Occupancy_Trend>80 then 'Take Over' 
                  else 'Stabilised' 
             end Property_Strategy  
      from(
        select 'Lifetime_Occupancy_Trend' Parameter,
               try_divide(((select count(distinct unit_code) 
                 from gold_dev.edlh.fact_property_unitscorecard  
                 where trim(property_code)=(select property_code from dates))
                -(select count(*) occ_non_rev 
                  from cte_last_year 
                  where sstatus in ('Vacant Unrented Ready','Vacant Unrented Not Ready','Notice Unrented')  
                    and rnk=1))*100,(select count(distinct unit_code) 
                                     from gold_dev.edlh.fact_property_unitscorecard  
                                     where trim(property_code)=(select property_code from dates))) Lifetime_Occupancy_Trend,
               year_diff,month_diff 
        from dates
      )
    ) PIVOT (
      max(Property_Strategy) Property_Strategy
      FOR parameter IN ('Property_Strategy_This_Year','Property_Strategy_Last_Year')
    )
  `;
};

module.exports = {
  query,
  getImagesQuery,
  getPropertyStrategyQuery,
};
