const { app } = require("@azure/functions");
const { executeDatabricksQuery } = require("./utils/databricks");

const WillowBridgeWeeklyRoundUpMockData = require('../data/willow-bridge-weekly-round-up.json');
const SouthEastWeeklyRoundUpYTDMockData = require('../data/south-east-weekly-round-up-ytd.json');

app.http("getWeeklyRoundUpFilterOptions", {
  methods: ["GET"],
  authLevel: "anonymous",
  route: "weekly-round-up/filter-options",
  handler: async (request, context) => {
    let query = "";
    try {
      // query = `
      //   PUT THE QUERY HERE
      // `;

      // const result = await executeDatabricksQuery(query, context);
      // const sortArray = (arr) => (Array.isArray(arr) ? [...arr].sort() : []);
      // const years = [...new Set(result.map((row) => row.years).filter(Boolean))];
      // const months = [...new Set(result.map((row) => row.months).filter(Boolean))];
      // const departments = [...new Set(result.map((row) => row.departments).filter(Boolean))];
      // const businessTypes = [...new Set(result.map((row) => row.businessTypes).filter(Boolean))];
      // const marketleaders = [...new Set(result.map((row) => row.marketleaders).filter(Boolean))];
      // const adminBu = [
      //   ...new Set(result.map((row) => (row.propertyBu && row.adminName ? `${row.propertyBu}-${row.adminName}` : null)).filter(Boolean)),
      // ];

      return {
        jsonBody: {
          regions: [],
          territories: [],
          properties: [],
          clients: [],
          marketing_leads: [],
          rpms: [],
          statuses: [],
          leasing_statuses: [],
        },
      };
    } catch (error) {
      context.log(`Error in getWeeklyRoundUpFilterOptions: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getWillowBridgeAveragesData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/willow-bridge/averages",
  handler: async (request, context) => {
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      return {
        jsonBody: WillowBridgeWeeklyRoundUpMockData.averages,
      };
    } catch (error) {
      context.log(`Error in getWillowBridgeAveragesData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getWillowBridgeTotalsData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/willow-bridge/totals",
  handler: async (request, context) => {
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      return {
        jsonBody: WillowBridgeWeeklyRoundUpMockData.totals,
      };
    } catch (error) {
      context.log(`Error in getWillowBridgeTotalsData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getWillowBridgeTableData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/willow-bridge/table",
  handler: async (request, context) => {
    let query = '';
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      query = `
        select * from gold_dev.stg.stage_weekly_roundup_list
      `;
      const tableData = await executeDatabricksQuery(query, context);
      return {
        jsonBody: tableData,
      };
    } catch (error) {
      context.log(`Error in getWillowBridgeTableData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getSouthEastHotListTableData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/south-east-hot-list/table",
  handler: async (request, context) => {
    let query = '';
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      query = `
        select * from gold_dev.stg.stage_weekly_roundup_hotlist
      `;
      const tableData = await executeDatabricksQuery(query, context);
      return {
        jsonBody: tableData,
      };
    } catch (error) {
      context.log(`Error in getWillowBridgeTableData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getSouthEastYTDAveragesData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/south-east-ytd/averages",
  handler: async (request, context) => {
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      return {
        jsonBody: SouthEastWeeklyRoundUpYTDMockData.averages,
      };
    } catch (error) {
      context.log(`Error in getSouthEastYTDAveragesData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getSouthEastYTDTotalsData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/south-east-ytd/totals",
  handler: async (request, context) => {
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      return {
        jsonBody: SouthEastWeeklyRoundUpYTDMockData.totals,
      };
    } catch (error) {
      context.log(`Error in getSouthEastYTDTotalsData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getSouthEastYTDOccupancyTrendData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/south-east-ytd/occupancy-trend-chart",
  handler: async (request, context) => {
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      return {
        jsonBody: SouthEastWeeklyRoundUpYTDMockData.occupancyTrend,
      };
    } catch (error) {
      context.log(`Error in getSouthEastYTDOccupancyTrendData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getSouthEastYTDAppliedApprovedData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/south-east-ytd/applied-approved-chart",
  handler: async (request, context) => {
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      return {
        jsonBody: SouthEastWeeklyRoundUpYTDMockData.appliedApproved,
      };
    } catch (error) {
      context.log(`Error in getSouthEastYTDAppliedApprovedData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getSouthEastYTDMoveInMoveOutData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/south-east-ytd/move-in-move-out-chart",
  handler: async (request, context) => {
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      return {
        jsonBody: SouthEastWeeklyRoundUpYTDMockData.moveInMoveOut,
      };
    } catch (error) {
      context.log(`Error in getSouthEastYTDMoveInMoveOutData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getSouthEastYTDTableData", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "weekly-round-up/south-east-ytd/table",
  handler: async (request, context) => {
    let query = '';
    try {
      const payload = await request.json();
      console.log("Payload received:", payload);
      query = `
        select * from gold_dev.stg.stage_weekly_roundup_YTD
      `;
      const tableData = await executeDatabricksQuery(query, context);
      return {
        jsonBody: tableData,
      };
    } catch (error) {
      context.log(`Error in getSouthEastYTDTableData: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});
