const { app } = require("@azure/functions");
const { executeDatabricksQuery } = require("./utils/databricks");
const { authenticate, ROLES } = require("./utils/auth");

const normalizeToArray = (value) => {
  if (!value) return null;
  return Array.isArray(value) ? value : [value];
};

const buildInClause = (field, values) => {
  if (!values || values.length === 0) return null;
  const escapedValues = values.map((v) => `'${String(v).replace(/'/g, "''")}'`);
  return `${field} IN (${escapedValues.join(", ")})`;
};

app.http("getPropertyManagementYoy", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "property-management-yoy",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["a.is_active=true", "b.BusinessType='Property Management'"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes && businessTypes.length > 0 && !businessTypes.includes("Property Management")) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) {
          whereConditions[whereConditions.length - 1] = businessTypeClause;
        }
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus.map((bu) => bu.split("-")[0]).filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("TRIM(a.AdminBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 1;

      const startDate = `${yearFilter}-${String(maxSelectedMonth).padStart(2, "0")}-01`;
      const endDate = `${yearFilter}-${String(maxSelectedMonth).padStart(2, "0")}-01`;

      query = `
        with 
        dates as (select cast('${startDate}' as date) startdate,'${endDate}' as enddate),
        cte_dim_property AS (
          Select distinct BU, a.Region from gold_dev.edlh.dim_property a     
          JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.AdminBu) = TRIM(b.AdminBU)
          where ${whereClause}
        ),
        cte_NewInPlaceRent As(
        select distinct dp.Region,'New In_Place_rent' parameter,avg(effective_rent) NewIn_Place_rent
        from gold_dev.edlh.fact_lease_history lh
         inner join cte_dim_property dp on trim(lh.property_code)=trim(dp.BU) 
        where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
        and rnk=1
        Group By dp.region
        ),
        cte_PrevYrNewInPlaceRent As(
        select dp.Region,'Prev_Yr_New In_Place_rent' parameter,avg(effective_rent) Prev_NewIn_Place_rent
         from gold_dev.edlh.fact_lease_history lh
          inner join cte_dim_property dp on trim(lh.property_code)=trim(dp.BU) 
        where enddate>=add_months(CAST((select enddate from dates) AS DATE), -12) 
        and dtleasefrom<=add_months(CAST((select enddate from dates) AS DATE), -12)
        and rnk=1
        Group By dp.Region
        ),
        cte_RenewalInPlaceRent As (
        select dp.Region,'Renewal_In_Place_rent' parameter,avg(effective_rent) Renewal_In_Place_rent
        from gold_dev.edlh.fact_lease_history lh
        inner join cte_dim_property dp on trim(lh.property_code)=trim(dp.BU) 
        where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
        and rnk>1
        Group By dp.Region
        ),
        cte_PrevYrRenewalInPlaceRent As(
        select dp.Region,'Prev_Yr_Renewal_In_Place_rent' parameter,avg(effective_rent) PrevYr_Renewal_In_Place_rent
        from gold_dev.edlh.fact_lease_history lh
        inner join cte_dim_property dp on trim(lh.property_code)=trim(dp.BU)  
        where  enddate>=add_months(CAST((select enddate from dates) AS DATE), -12) 
        and dtleasefrom<=add_months(CAST((select enddate from dates) AS DATE), -12)
        and rnk>1
        Group By dp.Region
        ),
        cte_InPlaceRent As(
        select dp.Region,'In_Place_rent' parameter,avg(effective_rent) In_Place_rent
        from gold_dev.edlh.fact_lease_history lh
        inner join cte_dim_property dp on trim(lh.property_code)=trim(dp.BU) 
        where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
        and rnk>=1
        Group By dp.Region
        ),
        cte_PrevYrInPlaceRent As(
        select dp.Region,'Prev_Yr_In_Place_rent' parameter,avg(effective_rent) PrevYr_In_Place_rent
        from gold_dev.edlh.fact_lease_history lh
        inner join cte_dim_property dp on trim(lh.property_code)=trim(dp.BU)  
        where  enddate>=add_months(CAST((select enddate from dates) AS DATE), -12) 
        and dtleasefrom<=add_months(CAST((select enddate from dates) AS DATE), -12)
        and rnk>=1
        Group By dp.Region
        ),
        cte as (
          select property_code,unit_code,sstatus
        ,row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
         from gold_dev.edlh.fact_property_unitscorecard 
         where trim(unit_status) !='Excluded' and dtstart<=(select enddate from dates)  
        ),
        cte_OccCount As(
        select dp.Region,count(*) occ_count 
        from cte c1 inner join cte_dim_property dp on trim(c1.property_code)=trim(dp.BU)
        where sstatus in ('Occupied No Notice','Notice Unrented','Notice Rented') and rnk=1
        Group By dp.Region
        ),
        cte_OccDownCount As(
        select dp.Region,count(distinct c1.unit_code) occ_down_count  
        from gold_dev.edlh.fact_property_unitscorecard c1
        inner join cte_dim_property dp on trim(c1.property_code)=trim(dp.BU)
        where trim(sstatus) in ('Admin','Down','Model') 
        and (dtEnd>=(select enddate from dates) or dtend is null)
        Group By dp.Region
        ),
        cte_OccTotalCount As(
        select dp.Region,count(distinct unit_code) occ_total_count
        from gold_dev.edlh.fact_property_unitscorecard c1
        inner join cte_dim_property dp on trim(c1.property_code)=trim(dp.BU)
        where trim(unit_status) !='Excluded'
        Group By dp.Region
        ),
        cte_OccVacantCount As(
        select dp.Region,count(*) occ_count 
        from cte c1 inner join cte_dim_property dp on trim(c1.property_code)=trim(dp.BU)
        where sstatus in ('Vacant Unrented Ready','Vacant Unrented Not Ready','Notice Unrented') and rnk=1
        Group By dp.Region
        ),
        cte_OccTrend As(
        Select c1.Region,try_divide((c2.occ_total_count-c1.occ_count),c2.occ_total_count)*100 OccTrend 
        from cte_OccVacantCount c1
        Inner Join cte_OccTotalCount c2 on c1.region=c2.region
        ),
        ----SubMarket Occupancy----
        cte1 as(select market_name,property,property_id,unit,unit_count,unit_status,start_date,end_date,
        row_number()over (partition by market_name,property_id,unit order by end_date desc) rnk  
        from gold_dev.edlh.fact_msa_data 
        where start_date <=(select startdate from dates) and end_Date>=(select enddate from dates) 
        and datediff(getdate(),start_date)<120
        ),
        final_cte as(select market_name,property,property_id,unit_count
        ,count(distinct unit) as cnt
        from cte1 where rnk=1  and  trim(unit_status)='Vacant'  group by all
        ),
        property_level_occ as 
        (
          select market_name,property_id,property,unit_count,cnt,try_divide((unit_count-cnt)*100,unit_count) property_level_occ 
        from final_cte where unit_count>0
        ),
        cte_SubmarketOccupancy As(
        select avg(property_level_occ)  submarket_occupancy
        from property_level_occ group by all
        ),
        ------End of Submarket Occupancy--------------
        cte_RegionWiseParam AS (
        Select * from(
        Select c1.Region,'Occupancy' as parameter,try_divide(((c1.occ_count+c2.occ_down_count)*100),c3.occ_total_count) as ParamValue
        from cte_OccCount c1
        Inner Join cte_OccDownCount c2 on c1.region=c2.region
        Inner Join cte_OccTotalCount c3 on c1.region=c3.region
        Union All
        Select Region,'Variance to Submarket' as parameter, 
        (OccTrend-( Select ifnull(submarket_occupancy,0) from cte_SubmarketOccupancy))
        from cte_OccTrend
        Union All
        Select c1.Region,'New In Place Rent YOY Change' as parameter,try_divide((c1.NewIn_Place_Rent-c2.Prev_NewIn_Place_rent),c1.NewIn_Place_Rent)*100 as NewIn_Place_Rent_YOY_Change
        from cte_NewInPlaceRent c1 
        Inner Join cte_PrevYrNewInPlaceRent c2 on c1.region=c2.region
        Union All
        Select c3.Region,'Renewal In Place Rent YOY Change' as parameter,try_divide((c3.Renewal_In_Place_rent-c4.PrevYr_Renewal_In_Place_rent),c3.Renewal_In_Place_rent)*100 as Renewal_In_Place_YOY_Change
        from cte_RenewalInPlaceRent c3 
        Inner Join cte_PrevYrRenewalInPlaceRent c4 on c3.region=c4.region
        Union All
        Select c5.Region,'In Place Rent YOY Change' as parameter,try_divide((c5.In_Place_rent-c6.PrevYr_In_Place_rent),c5.In_Place_rent)*100 as In_Place_YOY_Change
        from cte_InPlaceRent c5 
        Inner Join cte_PrevYrInPlaceRent c6 on c5.region=c6.region
        )
        PIVOT(
          MAX(ParamValue) 
          FOR region in ('East','West','Central')
        )
        )
        Select *,((Ifnull(East,0)+ifnull(West,0)+ifnull(Central,0))/3) As Consolidated 
        from cte_RegionWiseParam
      `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getPropertyManagementMetrics: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getPropertyManagementNoi", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "property-management-noi",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["a.is_active=true", "b.BusinessType='Property Management'"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes && businessTypes.length > 0 && !businessTypes.includes("Property Management")) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) {
          whereConditions[whereConditions.length - 1] = businessTypeClause;
        }
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus.map((bu) => bu.split("-")[0]).filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("TRIM(a.AdminBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 1;

      const startDate = `${yearFilter}-${String(maxSelectedMonth).padStart(2, "0")}-01`;
      const endDate = `${yearFilter}-${String(maxSelectedMonth).padStart(2, "0")}-01`;

      query = `
        with
        dates1 as (select cast('${startDate}' as date) startdate,'${endDate}' as enddate),
        property_cte as (
          select bu,property,region from gold_dev.edlh.dim_property a
          JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.AdminBu) = TRIM(b.AdminBU)
          where ${whereClause}
        ),
        dates as(
        select 
        trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') as 1st_day_of_year
        ,last_day(add_months(CAST((select enddate from dates1) AS DATE), 0)) AS last_day_prev_month
        from dates1
        )
        select * from ( 
        select 'Rental Income' parameter,b.region
        ,try_divide((sum(smtd1*-1) -sum(sbudget))*100,sum(sbudget)) Rental_Income
        from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and (acct_code like '401%'
        or acct_code like '402%' or acct_code like '403%'  or acct_code like '404%' or acct_code like '405%'
        or acct_code like '40653%' or acct_code like '40654%' or acct_code like '40655%')
        group by all 
        union all 
        ----Income 
        select 'Income' parameter,b.region
        ,try_divide((sum(smtd1*-1) -sum(sbudget)) * 100, sum(sbudget)) Income 
        from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where  cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(Total_Income)='Total Income'
        group by all
        union all 
        ----Cont OPex 
        select 'Controllable Opex'parameter,b.region
        ,try_divide((sum(sbudget)-sum(smtd1*-1)) * 100, sum(sbudget)) Cont_Opex
          from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(finance1)='Controllabe Op Exp'
        group by all 
        union all 
        ----total  OPex 
        select 'Total Opex' parameter,b.region
        ,try_divide((sum(sbudget)-sum(smtd1*-1)) * 100, sum(sbudget)) Total_Opex
          from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(total_opex)='Total Opex'
        group by all 
        union all 
        ----NOI 
        select 'NOI' parameter,b.region
        ,try_divide((sum(smtd1*-1)-sum(sbudget)) * 100, sum(sbudget)) NOI
        from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
         where cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(NOI)='NOI'
        group by all 
        union all 
        ----Controll NOI 
        select 'Controll NOI' parameter,b.region
        ,try_divide((sum(smtd1*-1)-sum(sbudget)) * 100, sum(sbudget)) Income
          from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where  cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(Controllable_NOI)='Controllable NOI'
        group by all 
        union all 
        select 'Rental Income' parameter,'Consolidated'
        ,try_divide((sum(smtd1*-1) -sum(sbudget))*100,sum(sbudget)) Rental_Income
        from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and (acct_code like '401%'
        or acct_code like '402%' or acct_code like '403%'  or acct_code like '404%' or acct_code like '405%'
        or acct_code like '40653%' or acct_code like '40654%' or acct_code like '40655%')
        group by all 
        union all 
        ----Income 
        select 'Income' parameter,'Consolidated'
        ,try_divide((sum(smtd1*-1) -sum(sbudget)) * 100, sum(sbudget)) Income 
        from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where  cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(Total_Income)='Total Income'
        group by all
        union all 
        ----Cont OPex 
        select 'Controllable Opex'parameter,'Consolidated'
        ,try_divide((sum(sbudget)-sum(smtd1*-1)) * 100, sum(sbudget)) Cont_Opex
          from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(finance1)='Controllabe Op Exp'
        group by all 
        union all 
        ----total  OPex 
        select 'Total Opex' parameter,'Consolidated'
        ,try_divide((sum(sbudget)-sum(smtd1*-1)) * 100, sum(sbudget)) Total_Opex
          from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(total_opex)='Total Opex'
        group by all 
        union all 
        ----NOI 
        select 'NOI' parameter,'Consolidated'
        ,try_divide((sum(smtd1*-1)-sum(sbudget)) * 100, sum(sbudget)) NOI
        from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
         where cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(NOI)='NOI'
        group by all 
        union all 
        ----Controll NOI 
        select 'Controll NOI' parameter,'Consolidated'
        ,try_divide((sum(smtd1*-1)-sum(sbudget)) * 100, sum(sbudget)) Income
          from gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where  cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(Controllable_NOI)='Controllable NOI'
        group by all 
        )pivot(max(rental_income) for region in ('East','West','Central','Consolidated'))
      `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getPropertyManagementNoi: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getPropertyManagementJTurner", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "property-management-jturner",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["a.is_active=true", "b.BusinessType='Property Management'"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes && businessTypes.length > 0 && !businessTypes.includes("Property Management")) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) {
          whereConditions[whereConditions.length - 1] = businessTypeClause;
        }
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus.map((bu) => bu.split("-")[0]).filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("TRIM(a.AdminBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 1;

      const startDate = `${yearFilter}-${String(maxSelectedMonth).padStart(2, "0")}-01`;
      const endDate = `${yearFilter}-${String(maxSelectedMonth).padStart(2, "0")}-01`;

      query = `
        with
        dates1 as (select cast('${startDate}' as date) startdate,'${endDate}' as enddate  ),
        property_cte as (
          select bu,property,a.region,google_place_id 
          from gold_dev.edlh.dim_property a
          JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.AdminBu) = TRIM(b.AdminBU)
          where ${whereClause}
        ),
        dates as(
        select cast((select enddate from dates1) as date)-30 t_30
        ,cast((select enddate from dates1) as date)-30 t_31
        , startdate
        , enddate  enddate
         ,cast((select enddate from dates1) as date)-90 t_90
        ,add_months(cast((select enddate from dates1) as date),-12) as prevyrenddate 
        , cast((select enddate from dates1) as date)-60 t_60
        ,trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') as 1st_day_of_year
        ,last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)) AS last_day_prev_month
        ,trunc(CAST((select enddate from dates1) AS DATE), 'MM') AS first_day_of_month  
        from dates1
        ) ,
        cte_property_closing_days AS (
          SELECT DISTINCT
            TRIM(property_code) AS property_code,
            CAST(icloseday AS INT) AS iclose_day
            ,date_Sub(trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') ,31-CAST(icloseday AS INT)) calc_start_date
            ,case when day(enddate) > CAST(icloseday AS INT) 
                  then MAKE_DATE(YEAR(i.enddate), MONTH(i.enddate), CAST(icloseday AS INT) ) 
                  when MONTH(i.enddate)=1
              then MAKE_DATE(YEAR(add_months(i.enddate,-12)), MONTH(add_months(i.enddate,-1)), CAST(icloseday AS INT) )
              when day(last_day(add_months(i.enddate,-1)))<CAST(icloseday AS INT)
            then MAKE_DATE(YEAR(i.enddate), MONTH(i.enddate)-1, day(last_day(add_months(i.enddate,-1))))
              else MAKE_DATE(YEAR(i.enddate), MONTH(i.enddate)-1, CAST(icloseday AS INT) ) end calc_end_date
          FROM gold_dev.edlh.fact_financial_perf_ytd p
          CROSS JOIN dates i
        ),
        cte_repeat_tickets as (select  b.bu,b.region,concat(unit_hmy,sub_category,category) cat_hmy,max(dt_Call)dt_Call
        from gold_dev.edlh.fact_property_tickets vspt
        left join property_cte b on trim(vspt.property_code)=trim(b.bu)
        where  dt_call between (select t_30 from dates) and (select enddate from dates) and date_cancel  is null and unit_hmy >0
         group by concat(unit_hmy,sub_category,category),b.region,b.bu
        )
        ,cte_outstanding as (select  b.region,vspt.*,TIMESTAMPDIFF(hour,dt_call,case when date_comp is null then (select enddate from dates) else date_comp end) date_diff1
        from gold_dev.edlh.fact_property_tickets vspt 
        left join property_cte b on trim(vspt.property_code)=trim(b.bu)
        where  dt_call between (select t_30 from dates) and (select enddate from dates) and date_cancel  is null 
        ) 
        select * from ( 
        select 'Avg Cost/Turn' parameter,p.region,try_divide(sum(q.actual),sum(tenant_cnt) )value from(
        select a.property_code,b.region,c.calc_start_date,c.calc_end_date,count(distinct tenant_code) tenant_cnt 
        from gold_dev.edlh.fact_lease_history a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        left join cte_property_closing_days c on trim(a.property_code)=trim(c.property_code) 
        and  dtmoveout>=c.calc_start_date  
        and dtmoveout<=c.calc_end_date 
        group by all) p
        left join (select a.property_code,b.region,sum(smtd1) actual
         From gold_dev.edlh.fact_financial_perf_ytd a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        left join cte_property_closing_days c on trim(a.property_code)=trim(c.property_code) 
        and  cast(umonth as date)>=c.calc_start_date  and cast(umonth as date)<=c.calc_end_date 
        where cost='Cost Per Turn' and
         ibook=1 group by all ) q on trim(p.property_code)=trim(q.property_code) and p.region=q.region
         group by all 
        union all 
        ---collection MTD
        select a.parameter,a.region,try_divide((a.receipt_amount*100),b.charge_amount) value from ( 
        select 'Collection % MTD' Parameter,b.region,
         sum(receipt_amount) receipt_amount 
        from gold_dev.edlh.fact_property_transactions a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where receipt_date<=(select enddate from dates) 
        and charge_date=(select first_day_of_month  from dates) 
        group by all ) a
        left join (select region,sum(charge_amount) charge_amount from (select distinct b.region,charge_key,charge_amount 
        from gold_dev.edlh.fact_property_transactions a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where charge_date=(select first_day_of_month  from dates) 
        ) group by all ) b on a.region=b.region
         union all 
         ---- avg turn time 
         select 
        'Average Unit Turn Time T90'parameter,b.region,avg(DATEDIFF(day,dtmoveout,dtready)) avg_turn_time
        From gold_dev.edlh.fact_property_unitscorecard a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where  trim(unit_status) !='Excluded'
        and dtready >=(select t_90 from dates) 
        and dtready<=(select enddate from dates) 
        and dtmoveout is not null
        group by all 
        union all 
        -----repeat tickets 
        select 'Units With Repeat Service Tickets Within 30 Days'parameter,region,count(*) repeat_tickets from(
        select a.property_code,cte.region,a.unit_hmy ,concat(sub_category,category),count(*)
        from gold_dev.edlh.fact_property_tickets a
        join cte_repeat_tickets cte on concat(a.unit_hmy,a.sub_category,a.category) =cte.cat_hmy and trim(a.property_code)=trim(cte.bu)
        and a.dt_call between cast(cte.dt_call as date)-30 and cte.dt_Call
        where a.dt_call between (select t_60 from dates) and (select enddate from dates) and date_cancel  is null
        group by  a.property_code,cte.region,a.unit_hmy ,concat(sub_category,category)
         having count(*)>1
        ) group by all 
        union all 
        select '% Tickets >72 Hours to Close T30',region,try_divide(sum(case when date_diff1>72 then 1 else 0 end ) * 100,
        sum(case when date_diff1 is not null then 1 else 0 end)) value1
         from cte_outstanding group by all 
        union all 
        select 'J Turner' parameter,b.region,avg(try_cast(ora_score as double))ora_score 
        from silver_dev.jturner.ora_scores a
        left join property_cte b on trim(a.client_id)=trim(b.bu)
        group by all 
        union all 
        select 'Google' Parameter,b.region, avg(try_cast(rating as double)) rating
        from silver_dev.google_api.vw_google_ratings a
        left join property_cte b on trim(a.placeid)=trim(b.google_place_id)
        group by all 
        union all 
        select 'Avg Cost/Turn' parameter,p.region,try_divide(sum(q.actual),sum(tenant_cnt) )value from(
        select a.property_code,'Consolidated' region,c.calc_start_date,c.calc_end_date,count(distinct tenant_code) tenant_cnt 
        from gold_dev.edlh.fact_lease_history a
        left join cte_property_closing_days c on trim(a.property_code)=trim(c.property_code) 
        and  dtmoveout>=c.calc_start_date  
        and dtmoveout<=c.calc_end_date 
        group by all) p
        left join (select a.property_code,'Consolidated' region,sum(smtd1) actual
         From gold_dev.edlh.fact_financial_perf_ytd a
        left join cte_property_closing_days c on trim(a.property_code)=trim(c.property_code) 
        and  cast(umonth as date)>=c.calc_start_date  and cast(umonth as date)<=c.calc_end_date 
        where cost='Cost Per Turn' and
         ibook=1 group by all ) q on trim(p.property_code)=trim(q.property_code) 
         group by all 
        union all 
        ---collection MTD
        select a.parameter,a.region,try_divide((a.receipt_amount*100),b.charge_amount) value from ( 
        select 'Collection % MTD' Parameter,'Consolidated' region,
         sum(receipt_amount) receipt_amount 
        from gold_dev.edlh.fact_property_transactions a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where receipt_date<=(select enddate from dates) 
        and charge_date=(select first_day_of_month  from dates) 
        group by all ) a
        left join (select region,sum(charge_amount) charge_amount from (select distinct 'Consolidated' region,charge_key,charge_amount 
        from gold_dev.edlh.fact_property_transactions a
        left join property_cte b on trim(a.property_code)=trim(b.bu)
        where charge_date=(select first_day_of_month  from dates) 
        ) group by all ) b on a.region=b.region
         union all 
         ---- avg turn time 
         select 
        'Average Unit Turn Time T90'parameter,'Consolidated'region,avg(DATEDIFF(day,dtmoveout,dtready)) avg_turn_time
        From gold_dev.edlh.fact_property_unitscorecard 
        where  trim(unit_status) !='Excluded'
        and dtready >=(select t_90 from dates) 
        and dtready<=(select enddate from dates) 
        and dtmoveout is not null
        group by all 
        union all 
        -----repeat tickets 
        select 'Units With Repeat Service Tickets Within 30 Days'parameter,'Consolidated' region,count(*) repeat_tickets from(
        select a.property_code,a.unit_hmy ,concat(sub_category,category),count(*)
        from gold_dev.edlh.fact_property_tickets a
        join cte_repeat_tickets cte on concat(a.unit_hmy,a.sub_category,a.category) =cte.cat_hmy and trim(a.property_code)=trim(cte.bu)
        and a.dt_call between cast(cte.dt_call as date)-30 and cte.dt_Call
        where a.dt_call between (select t_60 from dates) and (select enddate from dates) and date_cancel  is null
        group by  a.property_code,a.unit_hmy ,concat(sub_category,category)
         having count(*)>1
        ) group by all 
        union all 
        select '% Tickets >72 Hours to Close T30','Consolidated' region,try_divide(sum(case when date_diff1>72 then 1 else 0 end ) * 100,
        sum(case when date_diff1 is not null then 1 else 0 end)) value1
         from cte_outstanding group by all 
        union all 
        select 'J Turner' parameter,'Consolidated' region,avg(try_cast(ora_score as double))ora_score 
        from silver_dev.jturner.ora_scores a
        group by all 
        union all 
        select 'Google' Parameter,'Consolidated' region, avg(try_cast(rating as double)) rating
        from silver_dev.google_api.vw_google_ratings a
        group by all 
        )pivot(max(value) for region in ('Central','East','West','Consolidated'))
      `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getPropertyManagementJTurner: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});
