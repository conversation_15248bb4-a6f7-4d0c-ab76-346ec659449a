const { DBSQLClient } = require("@databricks/sql");

async function getDatabricksConfig(context) {
  try {
    // const keyVaultClient = initKeyVaultClient();

    // Retrieve secrets from Key Vault
    // const workspaceUrl = (await keyVaultClient.getSecret("DATABRICKS-WORKSPACE-URL")).value;
    // const token = (await keyVaultClient.getSecret("DATABRICKS-TOKEN")).value;
    // const warehouseId = (await keyVaultClient.getSecret("DATABRICKS-WAREHOUSE-ID")).value;

    const workspaceUrl = process.env["DATABRICKS_WORKSPACE_URL"];
    const token = process.env["DATABRICKS_TOKEN"];
    const warehouseId = process.env["DATABRICKS_WAREHOUSE_ID"];
    // context.log("Databricks configuration:", workspaceUrl, token, warehouseId);

    if (!workspaceUrl || !token || !warehouseId) {
      throw new Error("One or more Databricks configuration values are missing from Key Vault");
    }

    return {
      workspaceUrl,
      token,
      warehouseId,
    };
  } catch (error) {
    context.error("Error retrieving Databricks configuration from Key Vault:", error);
    throw error;
  }
}

async function executeDatabricksQuery(query, context) {
  const client = new DBSQLClient();

  try {
    // Get Databricks configuration from Key Vault
    const databricksConfig = await getDatabricksConfig(context);

    // Connection configuration
    const connectionOptions = {
      host: databricksConfig.workspaceUrl,
      path: "/sql/1.0/warehouses/" + databricksConfig.warehouseId,
      token: databricksConfig.token,
    };

    context.log("Connecting to Databricks SQL warehouse...");
    await client.connect(connectionOptions);

    context.log("Executing query:", query);
    const session = await client.openSession();

    // Execute the query
    const queryOperation = await session.executeStatement(query, {
      runAsync: true,
      maxRows: 10000, // Enable direct results feature
    });

    // Fetch results
    const result = await queryOperation.fetchAll();

    // Close operations
    await queryOperation.close();
    await session.close();

    return result;
  } catch (error) {
    context.error("Error executing Databricks query:", error);
    throw error;
  } finally {
    // Ensure connection is closed regardless of success or failure
    try {
      await client.close();
    } catch (closeError) {
      context.error("Error closing Databricks connection:", closeError);
    }
  }
}

module.exports = {
  getDatabricksConfig,
  executeDatabricksQuery,
};
