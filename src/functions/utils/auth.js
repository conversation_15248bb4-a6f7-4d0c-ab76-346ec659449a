const { verify } = require("azure-ad-jwt");
const { executeDatabricksQuery } = require("./databricks");

const ROLES = {
  ADMIN: "Admin",
  DEVELOPER: "Developer",
  READER: "Reader",
  SCORECARD_READER: "Scorecard-Reader",
};

const jwtConfig = {
  env: {
    issuer: "https://sts.windows.net/be5cc231-0355-4ece-bc68-9fb5f4c9e31b/",
  },
};

const currentConfig = jwtConfig.env;

const authenticate = async (request, context, allowedRoles = []) => {
  const authorizationHeader = request.headers.get("authorization");
  if (!authorizationHeader) {
    throw new Error("Unauthorized");
  }

  const token = authorizationHeader.split(" ")[1];
  try {
    const tokenResult = await new Promise((resolve, reject) => {
      verify(token, currentConfig.issuer, function (err, result) {
        if (result) {
          resolve(result);
        } else {
          context.log(`Token validation failed: ${err}`);
          reject(err);
        }
      });
    });

    const roles = tokenResult.roles || [];
    if (!roles.some((role) => allowedRoles.includes(role))) {
      throw new Error("Forbidden");
    }

    return tokenResult;
  } catch (error) {
    context.log(`Token validation failed: ${error.message}`);
    throw new Error(`${error.message}`);
  }
};

const validatePropertyAccess = async (request, context, propertyCodeFields = ["propertyCode", "propertyCodes"], userEmail = null) => {
  try {
    if (!userEmail) {
      context.log("User email not found in token");
      return {
        success: false,
        response: {
          status: 401,
          jsonBody: {
            error: "Authentication failed",
            message: "User email not found in token",
          },
        },
      };
    }

    const requestBody = await request.json();
    let rawPropertyCodes = null;

    for (const field of propertyCodeFields) {
      if (requestBody[field] !== undefined && requestBody[field] !== null && requestBody[field] !== "") {
        rawPropertyCodes = requestBody[field];
        break;
      }
    }

    if (!rawPropertyCodes) {
      context.log("Property code not found in request body");
      return {
        success: false,
        response: {
          status: 400,
          jsonBody: {
            error: "Property code is required",
            message: `One of the following fields is required: propertyCode or propertyCodes`,
          },
        },
      };
    }

    let propertyCodes = [];
    if (Array.isArray(rawPropertyCodes)) {
      // Handle array format: ["67015", "22514"]
      propertyCodes = rawPropertyCodes.filter((code) => code && code.toString().trim() !== "");
    } else if (typeof rawPropertyCodes === "string") {
      // Handle comma-separated string format: "67015,22514" or single code: "67015"
      propertyCodes = rawPropertyCodes
        .split(",")
        .map((code) => code.trim())
        .filter((code) => code !== "");
    } else {
      // Handle single non-string values (numbers, etc.)
      const codeStr = rawPropertyCodes.toString().trim();
      if (codeStr !== "") {
        propertyCodes = [codeStr];
      }
    }

    if (propertyCodes.length === 0) {
      context.log("No valid property codes found in request body");
      return {
        success: false,
        response: {
          status: 400,
          jsonBody: {
            error: "Valid property code is required",
            message: "At least one valid property code must be provided",
          },
        },
      };
    }

    // Validate property access using SQL query
    const validationQuery = `
      SELECT user_email, role, EXPLODE(SPLIT(property_list, ',')) AS property_code
      FROM config_dev.gold.master_report_authentication
      WHERE user_email = '${userEmail.replace(/'/g, "''")}'
    `;

    // context.log(`Validating property access for user: ${userEmail}, properties: [${propertyCodes.join(", ")}]`);
    const validationResult = await executeDatabricksQuery(validationQuery, context);

    const userAuthorizedCodes = validationResult.map((row) => (row.property_code ? row.property_code.trim() : null)).filter((code) => code !== null);

    const unauthorizedCodes = [];
    const authorizedCodes = [];

    for (const requestedCode of propertyCodes) {
      const trimmedRequestedCode = requestedCode.trim();
      const hasAccess = userAuthorizedCodes.some((authorizedCode) => authorizedCode === trimmedRequestedCode);

      if (hasAccess) {
        authorizedCodes.push(trimmedRequestedCode);
      } else {
        unauthorizedCodes.push(trimmedRequestedCode);
      }
    }

    if (unauthorizedCodes.length > 0) {
      context.log(`Access denied for user ${userEmail} to properties: [${unauthorizedCodes.join(", ")}]`);
      return {
        success: false,
        response: {
          status: 403,
          jsonBody: {
            error: "Access denied",
            message:
              unauthorizedCodes.length === 1
                ? `You do not have access to property code: ${unauthorizedCodes[0]}`
                : `You do not have access to the following property codes: ${unauthorizedCodes.join(", ")}`,
            unauthorizedCodes: unauthorizedCodes,
            authorizedCodes: authorizedCodes,
          },
        },
      };
    }

    context.log(`Access granted for user ${userEmail} to all requested properties: [${authorizedCodes.join(", ")}]`);
    return {
      success: true,
      userEmail: userEmail,
      propertyCodes: authorizedCodes,
      propertyCode: authorizedCodes[0],
      requestBody: requestBody,
    };
  } catch (error) {
    context.log(`Error in validatePropertyAccess: ${error.message}`);
    return {
      success: false,
      response: {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      },
    };
  }
};

module.exports = { authenticate, validatePropertyAccess, ROLES };
