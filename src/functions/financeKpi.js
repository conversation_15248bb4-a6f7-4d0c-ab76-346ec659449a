const { app } = require("@azure/functions");
const { executeDatabricksQuery } = require("./utils/databricks");
const {
  normalizeToArray,
  buildInClause,
  getFinanceKPIRegionwiseQuery,
  getFinanceKPIMarketRegionForecastQuery,
  getFinanceKPIMarketRegionActualQuery,
  getFinanceKpiYtdActualVsBudgetQuery,
} = require("./financeKpiQuery");

app.http("getFinanceKPIRegionwise", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "finance-kpi-regionwise",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus
          .filter((bu) => bu !== null && bu !== undefined)
          .map((bu) => String(bu).split("-")[0])
          .filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("trim(a.propertyBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 1;
      const selectedMonth = parseInt(yearFilter) === currentYear ? Math.min(maxSelectedMonth, currentMonth) : maxSelectedMonth;

      let monthFilter;
      if (months && months.length > 0) {
        const maxMonth = Math.max(...months.map((m) => parseInt(m)));
        const adjustedMaxMonth = parseInt(yearFilter) === currentYear ? Math.min(maxMonth, currentMonth) : maxMonth;
        const monthRange = Array.from({ length: adjustedMaxMonth }, (_, i) => i + 1);
        monthFilter = `(${monthRange.join(",")})`;
      } else {
        monthFilter = "(1)";
      }

      const forecastStartMonth = selectedMonth + 1;
      const actualMonthRange = Array.from({ length: selectedMonth }, (_, i) => i + 1).join(",");

      query = getFinanceKPIRegionwiseQuery(yearFilter, whereClause, monthFilter, forecastStartMonth, actualMonthRange, selectedMonth);

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getFinanceKPIRegionwise: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getFinanceKPIMarketRegionForecast", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "finance-kpi-market-region-forecast",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus
          .filter((bu) => bu !== null && bu !== undefined)
          .map((bu) => String(bu).split("-")[0])
          .filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("trim(a.propertyBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 1;
      const selectedMonth = parseInt(yearFilter) === currentYear ? Math.min(maxSelectedMonth, currentMonth) : maxSelectedMonth;

      let monthFilter;
      if (months && months.length > 0) {
        const maxMonth = Math.max(...months.map((m) => parseInt(m)));
        const adjustedMaxMonth = parseInt(yearFilter) === currentYear ? Math.min(maxMonth, currentMonth) : maxMonth;
        const monthRange = Array.from({ length: adjustedMaxMonth }, (_, i) => i + 1);
        monthFilter = `(${monthRange.join(",")})`;
      } else {
        monthFilter = "(1)";
      }

      const forecastStartMonth = selectedMonth + 1;
      const actualMonthRange = Array.from({ length: selectedMonth }, (_, i) => i + 1).join(",");

      query = getFinanceKPIMarketRegionForecastQuery(yearFilter, whereClause, monthFilter, forecastStartMonth, actualMonthRange, selectedMonth);

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getFinanceKPIMarketRegionForecast: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getFinanceKPIMarketRegionActual", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "finance-kpi-market-region-actual",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus
          .filter((bu) => bu !== null && bu !== undefined)
          .map((bu) => String(bu).split("-")[0])
          .filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("trim(a.propertyBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;

      const ptdMonthFilter = months && months.length > 0 ? `(${months.join(",")})` : `(${currentMonth})`;

      const currentYear = currentDate.getFullYear();
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 1;
      const selectedMonth = parseInt(yearFilter) === currentYear ? Math.min(maxSelectedMonth, currentMonth) : maxSelectedMonth;

      query = getFinanceKPIMarketRegionActualQuery(yearFilter, whereClause, ptdMonthFilter, selectedMonth);

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getFinanceKPIMarketRegionActual: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getFinanceKpiYtdActualVsBudget", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "finance-kpi-ytd-actual-vs-budget",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "2", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus
          .filter((bu) => bu !== null && bu !== undefined)
          .map((bu) => String(bu).split("-")[0])
          .filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("trim(a.propertyBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 2;
      const selectedMonth = parseInt(yearFilter) === currentYear ? Math.min(maxSelectedMonth, currentMonth) : maxSelectedMonth;

      let monthFilter;
      if (months && months.length > 0) {
        const maxMonth = Math.max(...months.map((m) => parseInt(m)));
        const adjustedMaxMonth = parseInt(yearFilter) === currentYear ? Math.min(maxMonth, currentMonth) : maxMonth;
        const monthRange = Array.from({ length: adjustedMaxMonth }, (_, i) => i + 1);
        monthFilter = `(${monthRange.join(",")})`;
      } else {
        monthFilter = "(1,2)";
      }

      const forecastStartMonth = selectedMonth + 1;
      const actualMonthRange = Array.from({ length: selectedMonth }, (_, i) => i + 1).join(",");

      query = getFinanceKpiYtdActualVsBudgetQuery(yearFilter, whereClause, monthFilter, forecastStartMonth, actualMonthRange, selectedMonth);

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getFinanceKpiYtdActualVsBudget: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});
