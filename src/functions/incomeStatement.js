const { app } = require("@azure/functions");
const { executeDatabricksQuery } = require("./utils/databricks");
const { authenticate, ROLES } = require("./utils/auth");

const normalizeToArray = (value) => {
  if (!value) return null;
  return Array.isArray(value) ? value : [value];
};

const buildInClause = (field, values) => {
  if (!values || values.length === 0) return null;
  const escapedValues = values.map((v) => `'${String(v).replace(/'/g, "''")}'`);
  return `${field} IN (${escapedValues.join(", ")})`;
};

app.http("getStatementOfOperationsYTD", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "statement-of-operations-ytd",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus.map((bu) => bu.split("-")[0]).filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("trim(a.propertyBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 2;
      const selectedMonth = parseInt(yearFilter) === currentYear ? Math.min(maxSelectedMonth, currentMonth) : maxSelectedMonth;

      let monthFilter;
      if (months && months.length > 0) {
        const maxMonth = Math.max(...months.map((m) => parseInt(m)));
        const monthRange = Array.from({ length: maxMonth }, (_, i) => i + 1);
        monthFilter = `(${monthRange.join(",")})`;
      } else {
        monthFilter = "(2)";
      }

      const forecastStartMonth = selectedMonth + 1;
      const actualMonthRange = Array.from({ length: selectedMonth }, (_, i) => i + 1).join(",");

      query = `
      WITH cte_FYforecast AS (
        SELECT ExpandTotal, total,
               SUM(CASE WHEN MONTH(date) BETWEEN ${forecastStartMonth} AND 12 THEN forecast ELSE 0 END) + 
               SUM(CASE WHEN MONTH(date) IN (${actualMonthRange}) THEN actual ELSE 0 END) AS forecast,
               SUM(CASE WHEN YEAR(date)='${yearFilter}' THEN budget ELSE 0 END) AS budget
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE ${whereClause} AND YEAR(date)='${yearFilter}'
        GROUP BY ALL
      ),
      cte_CYActual AS (
        SELECT ExpandTotal, total,
               SUM(Actual) AS actual, 
               SUM(Budget) AS budget
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE ${whereClause} AND YEAR(date)='${yearFilter}' AND MONTH(date) IN ${monthFilter}
        GROUP BY ALL
      )
      SELECT REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
             a.Total,
             SUM(actual) AS Actual,
             SUM(a.budget) AS budget,
             SUM(actual) - SUM(a.budget) AS variance,
             try_divide((SUM(actual) - SUM(a.budget)) * 100, SUM(a.budget)) AS Variance_Pert,
             SUM(forecast) AS forecast,
             SUM(b.budget) AS budget1,
             SUM(forecast) - SUM(b.budget) AS Variance1,
             try_divide((SUM(forecast) - SUM(b.budget)) * 100, SUM(b.budget)) AS FY_Variance_Pert
      FROM cte_CYActual a
      LEFT JOIN cte_FYforecast b ON a.expandtotal = b.expandtotal AND a.total = b.total
      GROUP BY ALL
    `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getIncomeStatement: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getStatementOfOperationsPTD", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "statement-of-operations-ptd",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus.map((bu) => bu.split("-")[0]).filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("trim(a.propertyBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : currentMonth;
      const selectedYear = parseInt(yearFilter);

      const ytdEndMonth = maxSelectedMonth;

      const ptdMonthFilter = months && months.length > 0 ? `(${months.join(",")})` : `(${currentMonth})`;

      query = `
      WITH cte_YTDActual AS (
        SELECT ExpandTotal, total,
               SUM(actual) AS ytd_actual, 
               SUM(budget) AS ytd_budget 
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE YEAR(date) = '${yearFilter}' AND MONTH(date) BETWEEN 1 AND ${ytdEndMonth} AND ${whereClause}
        GROUP BY ALL
      ),
      cte_PTDActual AS (
        SELECT ExpandTotal, total,
               SUM(Actual) AS actual, 
               SUM(Budget) AS budget
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE ${whereClause} AND YEAR(date) = '${yearFilter}' AND MONTH(date) IN ${ptdMonthFilter}
        GROUP BY ALL 
      )
      SELECT REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
             a.Total,
             SUM(b.actual) AS PTD_Actual,
             SUM(b.budget) AS PTD_Budget,
             SUM(b.actual) - SUM(b.budget) AS PTD_Variance,
             try_divide((SUM(b.actual) - SUM(b.budget)) * 100, SUM(b.budget)) AS PTD_Variance_Perct,
             SUM(a.ytd_actual) AS YTD_Actual,
             SUM(a.ytd_budget) AS YTD_Budget,
             SUM(a.ytd_actual) - SUM(a.ytd_budget) AS YTD_Variance,
             try_divide((SUM(a.ytd_actual) - SUM(a.ytd_budget)) * 100, SUM(a.ytd_budget)) AS YTD_Variance_Perct
      FROM cte_YTDActual a
      LEFT JOIN cte_PTDActual b ON a.expandtotal = b.expandtotal AND a.total = b.total 
      GROUP BY ALL
    `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getStatementOfOperationsPTD: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getStatementOfOperationsByThousand", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "statement-of-operations-by-thousand",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus.map((bu) => bu.split("-")[0]).filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("trim(a.propertyBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 2;
      const selectedMonth = parseInt(yearFilter) === currentYear ? Math.min(maxSelectedMonth, currentMonth) : maxSelectedMonth;

      let monthFilter;
      if (months && months.length > 0) {
        const maxMonth = Math.max(...months.map((m) => parseInt(m)));
        const monthRange = Array.from({ length: maxMonth }, (_, i) => i + 1);
        monthFilter = `(${monthRange.join(",")})`;
      } else {
        monthFilter = "(2)";
      }

      const forecastStartMonth = selectedMonth + 1;
      const actualMonthRange = Array.from({ length: selectedMonth }, (_, i) => i + 1).join(",");

      query = `
      WITH cte_FYforecast AS (
        SELECT ExpandTotal, total,
               SUM(CASE WHEN MONTH(date) BETWEEN ${forecastStartMonth} AND 12 THEN forecast ELSE 0 END) + 
               SUM(CASE WHEN MONTH(date) IN (${actualMonthRange}) THEN actual ELSE 0 END) AS forecast,
               SUM(CASE WHEN YEAR(date)='${yearFilter}' THEN budget ELSE 0 END) AS budget
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE  b.businesstype='Property Management' and ${whereClause} AND YEAR(date)='${yearFilter}'
        GROUP BY ALL
      ),
      cte_CYActual AS (
        SELECT ExpandTotal, total, 
               SUM(Actual) AS actual, 
               SUM(Budget) AS budget
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE  b.businesstype='Property Management' and ${whereClause} AND YEAR(date)='${yearFilter}' AND MONTH(date) IN ${monthFilter}
        GROUP BY ALL
      ),
      cte_FYUnits as (select typee expandTotal, typee total,
        sum(case when month=12 then budget else 0 end) FY_budget,
        sum(case when month=12 then forecast else 0 end) FY_forecast
        from gold_dev.edlh.fact_property_details_report a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        where  typee='Units' and b.businesstype='Property Management' and ${whereClause} AND YEAR(date)='${yearFilter}'
        group by all ),
      cte_CYUnits as (select typee expandTotal, typee total,
        sum(actuals) Actual,
        sum(Budget) Budget
        from gold_dev.edlh.fact_property_details_report a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        where   typee='Units' and b.businesstype='Property Management' and ${whereClause} AND YEAR(date)='${yearFilter}' AND MONTH(date) = ${maxSelectedMonth}
        group by all)
      SELECT REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
             a.Total,
             SUM(actual) AS Actual,
             SUM(a.budget) AS budget,
             SUM(actual) - SUM(a.budget) AS variance,
             try_divide((SUM(actual) - SUM(a.budget)) * 100, SUM(a.budget)) AS Variance_Pert,
             SUM(forecast) AS forecast,
             SUM(b.budget) AS budget1,
             SUM(forecast) - SUM(b.budget) AS Variance1,
             try_divide((SUM(forecast) - SUM(b.budget)) * 100, SUM(b.budget)) AS FY_Variance_Pert
      FROM cte_CYActual a
      LEFT JOIN cte_FYforecast b ON a.expandtotal = b.expandtotal AND a.total = b.total
      GROUP BY ALL
      union all 
    select a.expandTotal
    ,b.total
    ,a.Actual
    ,a.Budget
    ,a.Actual-a.Budget Varience
    ,0 Variance_perct
    ,b.FY_forecast,b.FY_budget,b.FY_forecast-b.FY_budget FY_Varience
    ,0 FY_Variance_perct 
    from cte_CYUnits a
    left join cte_FYUnits b on a.expandTotal=b.expandtotal
    `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getIncomeStatement: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getStatementOfOperationsFilterOptions", {
  methods: ["GET"],
  authLevel: "anonymous",
  route: "statement-of-operations/filter-options",
  handler: async (request, context) => {
    let query = "";
    try {
      query = `
        select distinct year(date) years, month(date) months, propertyBu, b.adminBu, b.adminName, b.marketleader marketleaders, b.businessType businessTypes, b.department departments
        from gold_dev.edlh.fact_income_statement a
        left join gold_dev.edlh.vw_income_statement_slicer b on trim(a.propertyBu)=trim(b.AdminBU)
      `;

      const result = await executeDatabricksQuery(query, context);

      const sortArray = (arr) => (Array.isArray(arr) ? [...arr].sort() : []);

      const years = [...new Set(result.map((row) => row.years).filter(Boolean))];
      const months = [...new Set(result.map((row) => row.months).filter(Boolean))];
      const departments = [...new Set(result.map((row) => row.departments).filter(Boolean))];
      const businessTypes = [...new Set(result.map((row) => row.businessTypes).filter(Boolean))];
      const marketleaders = [...new Set(result.map((row) => row.marketleaders).filter(Boolean))];
      const adminBu = [
        ...new Set(result.map((row) => (row.propertyBu && row.adminName ? `${row.propertyBu}-${row.adminName}` : null)).filter(Boolean)),
      ];

      return {
        jsonBody: {
          sql: query,
          years: sortArray(years),
          months: sortArray(months),
          departments: sortArray(departments),
          businessTypes: sortArray(businessTypes),
          marketleaders: sortArray(marketleaders),
          adminBu: sortArray(adminBu),
        },
      };
    } catch (error) {
      context.log(`Error in getIncomeStatementFilterOptions: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getPropertyPackageFilters", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "property-package/filter-options",
  handler: async (request, context) => {
    let query = "";
    try {
      query = `
        select distinct year(date) years, month(date) months, propertyBu, b.adminBu, b.adminName, b.marketleader marketleaders, b.businessType businessTypes, b.department departments,b.regionFilter
        from gold_dev.edlh.fact_income_statement a
        left join gold_dev.edlh.vw_income_statement_slicer b on trim(a.propertyBu)=trim(b.AdminBU)
        where businessType='Property Management'
      `;

      const result = await executeDatabricksQuery(query, context);

      const sortArray = (arr) => (Array.isArray(arr) ? [...arr].sort() : []);

      const years = [...new Set(result.map((row) => row.years).filter(Boolean))];
      const months = [...new Set(result.map((row) => row.months).filter(Boolean))];
      const departments = [...new Set(result.map((row) => row.departments).filter(Boolean))];
      const businessTypes = [...new Set(result.map((row) => row.businessTypes).filter(Boolean))];
      const marketleaders = [...new Set(result.map((row) => row.marketleaders).filter(Boolean))];
      const regions = [...new Set(result.map((row) => row.regionFilter).filter(Boolean))];
      const adminBu = [
        ...new Set(result.map((row) => (row.propertyBu && row.adminName ? `${row.propertyBu}-${row.adminName}` : null)).filter(Boolean)),
      ];

      return {
        jsonBody: {
          sql: query,
          years: sortArray(years),
          months: sortArray(months),
          departments: sortArray(departments),
          businessTypes: sortArray(businessTypes),
          marketleaders: sortArray(marketleaders),
          regions: sortArray(regions),
          adminBu: sortArray(adminBu),
        },
      };
    } catch (error) {
      context.log(`Error in getPropertyPackageFilters: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getUnitsReport", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "units-report",
  handler: async (request, context) => {
    let query = "";
    try {
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["typee IN ('Units')"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (years) {
        const yearClause = buildInClause("a.year", years);
        if (yearClause) whereConditions.push(yearClause);
      }

      if (months) {
        const monthClause = buildInClause("a.month", months);
        if (monthClause) whereConditions.push(monthClause);
      }

      if (departments) {
        const departmentConditions = departments.map((dept) => `b.Department LIKE '%${String(dept).replace(/'/g, "''")}%'`);
        whereConditions.push(`(${departmentConditions.join(" OR ")})`);
      }

      if (businessTypes) {
        const businessTypeConditions = businessTypes.map((bt) => `b.BusinessType LIKE '%${String(bt).replace(/'/g, "''")}%'`);
        whereConditions.push(`(${businessTypeConditions.join(" OR ")})`);
      }

      if (marketleaders) {
        const marketleaderConditions = marketleaders.map((ml) => `b.MarketLeader LIKE '%${String(ml).replace(/'/g, "''")}%'`);
        whereConditions.push(`(${marketleaderConditions.join(" OR ")})`);
      }

      if (adminBus) {
        const propertyBus = adminBus.map((bu) => bu.split("-")[0]).filter(Boolean);
        if (propertyBus.length > 0) {
          const adminBuConditions = propertyBus.map((pb) => `a.AdminBU LIKE '%${String(pb).replace(/'/g, "''")}%'`);
          whereConditions.push(`(${adminBuConditions.join(" OR ")})`);
        }
      }

      const whereClause = whereConditions.length ? `WHERE ${whereConditions.join(" AND ")}` : "";

      query = `
        SELECT b.adminName, b.address, b.city, b.state, b.businessType, b.department, b.marketLeader, a.*
        FROM gold_dev.edlh.fact_property_details_report a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON a.adminBU = b.AdminBU
        ${whereClause}
      `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getUnitsReport: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          sql: query,
          message: error.message,
        },
      };
    }
  },
});

app.http("getStatementOfOperationsPTDDrillThrough", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "statement-of-operations-ptd-drill-through",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus.map((bu) => bu.split("-")[0]).filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("trim(a.propertyBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 2;
      const selectedMonth = parseInt(yearFilter) === currentYear ? Math.min(maxSelectedMonth, currentMonth) : maxSelectedMonth;

      const ytdEndMonth = maxSelectedMonth;
      const ptdMonthFilter = months && months.length > 0 ? `(${months.join(",")})` : `(${currentMonth})`;

      let monthFilter;
      if (months && months.length > 0) {
        const maxMonth = Math.max(...months.map((m) => parseInt(m)));
        const monthRange = Array.from({ length: maxMonth }, (_, i) => i + 1);
        monthFilter = `(${monthRange.join(",")})`;
      } else {
        monthFilter = "(2)";
      }

      const forecastStartMonth = selectedMonth + 1;
      const actualMonthRange = Array.from({ length: selectedMonth }, (_, i) => i + 1).join(",");

      query = `
      WITH cte_YTDActual AS (
        SELECT ExpandTotal, total, a.acctCode,replace(a.AcctCodeAndDesc,'.','') AcctCodeAndDesc,
               SUM(actual) AS ytd_actual,
               SUM(budget) AS ytd_budget
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE YEAR(date) = '${yearFilter}' AND MONTH(date) BETWEEN 1 AND ${ytdEndMonth} AND ${whereClause}
        GROUP BY ALL
      ),
      cte_PTDActual AS (
        SELECT ExpandTotal, total, a.acctCode,replace(a.AcctCodeAndDesc,'.','') AcctCodeAndDesc,
               SUM(Actual) AS actual,
               SUM(Budget) AS budget
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE ${whereClause} AND YEAR(date) = '${yearFilter}' AND MONTH(date) IN ${ptdMonthFilter}
        GROUP BY ALL
      )
      SELECT REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
             a.Total,
             a.acctCode,
             a.AcctCodeAndDesc,
             SUM(b.actual) AS PTD_Actual,
             SUM(b.budget) AS PTD_Budget,
             SUM(b.actual) - SUM(b.budget) AS PTD_Variance,
             try_divide((SUM(b.actual) - SUM(b.budget)) * 100, SUM(b.budget)) AS PTD_Variance_Perct,
             SUM(a.ytd_actual) AS YTD_Actual,
             SUM(a.ytd_budget) AS YTD_Budget,
             SUM(a.ytd_actual) - SUM(a.ytd_budget) AS YTD_Variance,
             try_divide((SUM(a.ytd_actual) - SUM(a.ytd_budget)) * 100, SUM(a.ytd_budget)) AS YTD_Variance_Perct
      FROM cte_YTDActual a
      LEFT JOIN cte_PTDActual b ON a.expandtotal = b.expandtotal AND a.total = b.total AND trim(a.acctCode) = trim(b.acctCode)
      GROUP BY ALL
    `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getStatementOfOperationsYTDDrillThrough: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getStatementOfOperationsYTDDrillThrough", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "statement-of-operations-ytd-drill-through",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { year = "2025", month = "1", department = null, businessType = null, marketleader = null, adminBu = null } = await request.json();

      const whereConditions = ["1=1"];

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);

      if (departments) {
        const departmentClause = buildInClause("b.Department", departments);
        if (departmentClause) whereConditions.push(departmentClause);
      }

      if (businessTypes) {
        const businessTypeClause = buildInClause("b.BusinessType", businessTypes);
        if (businessTypeClause) whereConditions.push(businessTypeClause);
      }

      if (marketleaders) {
        const marketleaderClause = buildInClause("b.MarketLeader", marketleaders);
        if (marketleaderClause) whereConditions.push(marketleaderClause);
      }

      if (adminBus) {
        const propertyBus = adminBus.map((bu) => bu.split("-")[0]).filter(Boolean);
        if (propertyBus.length > 0) {
          const propertyBuClause = buildInClause("trim(a.propertyBu)", propertyBus);
          if (propertyBuClause) whereConditions.push(propertyBuClause);
        }
      }

      const whereClause = whereConditions.join(" AND ");
      const yearFilter = years && years.length > 0 ? years[0] : "2025";
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 2;
      const selectedMonth = parseInt(yearFilter) === currentYear ? Math.min(maxSelectedMonth, currentMonth) : maxSelectedMonth;

      let monthFilter;
      if (months && months.length > 0) {
        const maxMonth = Math.max(...months.map((m) => parseInt(m)));
        const monthRange = Array.from({ length: maxMonth }, (_, i) => i + 1);
        monthFilter = `(${monthRange.join(",")})`;
      } else {
        monthFilter = "(2)";
      }

      const forecastStartMonth = selectedMonth + 1;
      const actualMonthRange = Array.from({ length: selectedMonth }, (_, i) => i + 1).join(",");

      query = `
      WITH cte_FYforecast AS (
        SELECT ExpandTotal, total, a.acctCode,replace(a.AcctCodeAndDesc,'.','') AcctCodeAndDesc,
               SUM(CASE WHEN MONTH(date) BETWEEN ${forecastStartMonth} AND 12 THEN forecast ELSE 0 END) + 
               SUM(CASE WHEN MONTH(date) IN (${actualMonthRange}) THEN actual ELSE 0 END) AS forecast,
               SUM(CASE WHEN YEAR(date)='${yearFilter}' THEN budget ELSE 0 END) AS budget
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE ${whereClause} AND YEAR(date)='${yearFilter}'
        GROUP BY ALL
      ),
      cte_CYActual AS (
        SELECT ExpandTotal, total, a.acctCode,replace(a.AcctCodeAndDesc,'.','') AcctCodeAndDesc,
               SUM(Actual) AS actual, 
               SUM(Budget) AS budget
        FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer b ON TRIM(a.propertyBu) = TRIM(b.AdminBU)
        WHERE ${whereClause} AND YEAR(date)='${yearFilter}' AND MONTH(date) IN ${monthFilter}
        GROUP BY ALL
      )
      SELECT REPLACE(a.ExpandTotal, '1', '') AS ExpandTotal,
             a.Total,
             a.acctCode,
             a.AcctCodeAndDesc,
             SUM(actual) AS Actual,
             SUM(a.budget) AS budget,
             SUM(actual) - SUM(a.budget) AS variance,
             try_divide((SUM(actual) - SUM(a.budget)) * 100, SUM(a.budget)) AS Variance_Pert,
             SUM(forecast) AS forecast,
             SUM(b.budget) AS budget1,
             SUM(forecast) - SUM(b.budget) AS Variance1,
             try_divide((SUM(forecast) - SUM(b.budget)) * 100, SUM(b.budget)) AS FY_Variance_Pert
      FROM cte_CYActual a
      LEFT JOIN cte_FYforecast b ON a.expandtotal = b.expandtotal AND a.total = b.total AND trim(a.acctCode) = trim(b.acctCode)
      GROUP BY ALL
    `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getStatementOfOperationsYTDDrillThrough: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});
