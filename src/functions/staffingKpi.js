const { app } = require("@azure/functions");
const { executeDatabricksQuery } = require("./utils/databricks");
const { getStaffingRpmAnalysisQuery } = require("./staffingKpiQuery");

const normalizeToArray = (value) => {
  if (!value) return null;
  return Array.isArray(value) ? value : [value];
};

const buildInClause = (field, values) => {
  if (!values || values.length === 0) return null;
  const escapedValues = values.map((v) => `'${String(v).replace(/'/g, "''")}'`);
  return `${field} IN (${escapedValues.join(", ")})`;
};

const getMaximumStartDate = (year, months) => {
  console.log("MONTHS >>>", months);
  const dates = (typeof months === 'string' ? [ months ] : months).map(m => {
    let month = `${m.length === 1 ? '0' : ''}${m}`;
    return `${year}-${month}-01`;
  }).sort().reverse();
  return dates[0];
};

app.http("getStaffingRpmKpi", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "staffing-kpi-rpm",
  handler: async (request, context) => {
    let query = "";
    try {
      const {
        year = "2025",
        month = "1",
        department = null,
        businessType = null,
        marketleader = null,
        adminBu = null,
        region = null,
        market = null,
      } = await request.json();

      query = getStaffingRpmAnalysisQuery(year, month, department, businessType, marketleader, adminBu, region, market);

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getStaffingRpmKpi: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getStaffingRegionalKpi", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "staffing-kpi-regional",
  handler: async (request, context) => {
    let query = "";
    try {
      const {
        year = "2025",
        month = "1",
        department = null,
        businessType = null,
        marketleader = null,
        adminBu = null,
        region = null,
      } = await request.json();

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);
      const regions = normalizeToArray(region);

      // Build year condition for the CTE
      let yearFilter = "2025";
      if (years && years.length > 0) {
        yearFilter = years[0];
      }

      // Build month condition for the CTE
      let monthFilter = "(5)";
      if (months && months.length > 0) {
        monthFilter = `(${months.join(", ")})`;
      }

      // Build additional WHERE conditions for property and employee filters
      const additionalConditions = [];

      if (departments && departments.length > 0) {
        const departmentClause = buildInClause("p.department", departments);
        if (departmentClause) additionalConditions.push(departmentClause);
      }

      if (businessTypes && businessTypes.length > 0) {
        const businessTypeClause = buildInClause("p.businesstype", businessTypes);
        if (businessTypeClause) additionalConditions.push(businessTypeClause);
      }

      if (marketleaders && marketleaders.length > 0) {
        const marketleaderClause = buildInClause("p.marketleader", marketleaders);
        if (marketleaderClause) additionalConditions.push(marketleaderClause);
      }

      if (adminBus && adminBus.length > 0) {
        const adminBuClause = buildInClause("p.adminbu", adminBus);
        if (adminBuClause) additionalConditions.push(adminBuClause);
      }

      if (regions && regions.length > 0) {
        const regionClause = buildInClause("p.Region", regions);
        if (regionClause) additionalConditions.push(regionClause);
      }

      const additionalWhere = additionalConditions.length > 0 ? ` AND ${additionalConditions.join(" AND ")}` : "";

      const startFilter = getMaximumStartDate(year, month);

      query = `
        WITH
          dates1 as (Select last_day('${startFilter}') as enddate),
          cte_Properties AS (
            SELECT Region,count(distinct a.RPM) as RPM, Count(distinct VP) as VP,
            Count(distinct trim(vena.property_code)) as Property
            FROM gold_dev.edlh.dim_property a
            JOIN gold_dev.edlh.vw_income_statement_slicer p ON TRIM(a.AdminBu) = TRIM(p.AdminBU)
            LEFT JOIN gold_dev.edlh.fact_property_details_report vena ON TRIM(a.BU) = TRIM(vena.property_code)
            WHERE a.is_active=true and p.BusinessType='Property Management'
            and assettype is not null and lower(trim(assettype)) not in ('','hoa','retail','transactions','garage')
            and typee='Units' and actuals>0 and month(date) = (select Month(enddate) from dates1)
            and YEAR(date) = (select year(enddate) from dates1)
            ${additionalWhere}
            GROUP BY ALL
          ),
          cte_Actual AS (
            SELECT * FROM (
              SELECT Region, RPM, VP, Property, try_divide(Property,RPM) as PropertyPerRPM,
              try_divide(RPM,VP) as RPMPerVP
              FROM cte_Properties
            )
            PIVOT(
              Sum(RPM) RPMActual,
              Sum(VP) VPActual,
              Sum(Property) PropertyActual,
              Sum(PropertyPerRPM) PropertyPerRPMActual,
              Sum(RPMPerVP) RPMPerVPActual
              FOR Region IN ('Central','East','West')
            )
          )
        SELECT 'RPMs' parameter_name, ifnull(Central_RPMActual,0) as central_actual, ifnull(East_RPMActual,0) as east_actual, ifnull(West_RPMActual,0) as west_actual,
        (ifnull(Central_RPMActual,0)+ifnull(East_RPMActual,0)+ifnull(West_RPMActual,0)) as consolidated_actual
        FROM cte_Actual
        UNION ALL
        SELECT 'VPs' parameter_name, ifnull(Central_VPActual,0) as central_actual, ifnull(East_VPActual,0) as east_actual, ifnull(West_VPActual,0) as west_actual,
        (ifnull(Central_VPActual,0)+ifnull(East_VPActual,0)+ifnull(West_VPActual,0)) as consolidated_actual
        FROM cte_Actual
        UNION ALL
        SELECT 'Properties' parameter_name, ifnull(Central_PropertyActual,0) as central_actual, ifnull(East_PropertyActual,0) as east_actual, ifnull(West_PropertyActual,0) as west_actual,
        (ifnull(Central_PropertyActual,0)+ifnull(East_PropertyActual,0)+ifnull(West_PropertyActual,0)) as consolidated_actual
        FROM cte_Actual
        UNION ALL
        SELECT 'Properties/RPM' parameter_name, ifnull(Central_PropertyPerRPMActual,0) as central_actual, ifnull(East_PropertyPerRPMActual,0) as east_actual, ifnull(West_PropertyPerRPMActual,0) as west_actual,
        try_divide((ifnull(Central_PropertyActual,0)+ifnull(East_PropertyActual,0)+ifnull(West_PropertyActual,0)),(ifnull(Central_RPMActual,0)+ifnull(East_RPMActual,0)+ifnull(West_RPMActual,0))) as consolidated_actual
        FROM cte_Actual
        UNION ALL
        SELECT 'RPMs per VP' parameter_name, ifnull(Central_RPMPerVPActual,0) as central_actual, ifnull(East_RPMPerVPActual,0) as east_actual, ifnull(West_RPMPerVPActual,0) as west_actual,
        try_divide((ifnull(Central_RPMActual,0)+ifnull(East_RPMActual,0)+ifnull(West_RPMActual,0)),(ifnull(Central_VPActual,0)+ifnull(East_VPActual,0)+ifnull(West_VPActual,0))) as consolidated_actual
        FROM cte_Actual
        UNION ALL
        SELECT 'Workload/RPM' parameter_name, 0 as central_actual, 0 as east_actual, 0 as west_actual, 0 as consolidated_actual
        FROM cte_Actual
      `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getStaffingRegionalKpi: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getStaffingMarketKpi", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "staffing-kpi-market",
  handler: async (request, context) => {
    let query = "";
    try {
      const {
        year = "2025",
        month = "1",
        department = null,
        businessType = null,
        marketleader = null,
        adminBu = null,
        region = null,
        market = null,
      } = await request.json();

      const years = normalizeToArray(year);
      const months = normalizeToArray(month);
      const departments = normalizeToArray(department);
      const businessTypes = normalizeToArray(businessType);
      const marketleaders = normalizeToArray(marketleader);
      const adminBus = normalizeToArray(adminBu);
      const regions = normalizeToArray(region);
      const markets = normalizeToArray(market);

      // Build year condition for the CTE
      let yearFilter = "2025";
      if (years && years.length > 0) {
        yearFilter = years[0];
      }

      // Build month condition for the CTE
      let monthFilter = "(5)";
      if (months && months.length > 0) {
        monthFilter = `(${months.join(", ")})`;
      }

      // Build additional WHERE conditions for property and employee filters
      const additionalConditions = [];

      if (departments && departments.length > 0) {
        const departmentClause = buildInClause("p.department", departments);
        if (departmentClause) additionalConditions.push(departmentClause);
      }

      if (businessTypes && businessTypes.length > 0) {
        const businessTypeClause = buildInClause("p.businesstype", businessTypes);
        if (businessTypeClause) additionalConditions.push(businessTypeClause);
      }

      if (marketleaders && marketleaders.length > 0) {
        const marketleaderClause = buildInClause("p.marketleader", marketleaders);
        if (marketleaderClause) additionalConditions.push(marketleaderClause);
      }

      if (adminBus && adminBus.length > 0) {
        const adminBuClause = buildInClause("p.adminbu", adminBus);
        if (adminBuClause) additionalConditions.push(adminBuClause);
      }

      if (regions && regions.length > 0) {
        const regionClause = buildInClause("p.region", regions);
        if (regionClause) additionalConditions.push(regionClause);
      }

      if (markets && markets.length > 0) {
        const marketClause = buildInClause("p.market", markets);
        if (marketClause) additionalConditions.push(marketClause);
      }

      const additionalWhere = additionalConditions.length > 0 ? ` AND ${additionalConditions.join(" AND ")}` : "";

      const startFilter = getMaximumStartDate(year, month);

      query = `
        WITH
        dates1 as (Select last_day('${startFilter}') as enddate),
        cte_Properties AS (
          select vena.property_code as bu,a.adminbu,property,cast(BeginningOfOperations as date) as BeginningOfOperations
          ,region,a.rpm,vp,regionmarket
          From gold_Dev.edlh.dim_property a
          JOIN gold_dev.edlh.vw_income_statement_slicer p ON TRIM(a.AdminBu) = TRIM(p.AdminBU)
          JOIN gold_dev.edlh.fact_property_details_report vena ON TRIM(a.BU) = TRIM(vena.property_code)
          where a.is_active=true and p.BusinessType='Property Management'
          and assettype is not null and lower(trim(assettype)) not in ('','hoa','retail','transactions','garage')
          and typee='Units' and actuals>0 and month(date) = (select Month(enddate) from dates1)
          and YEAR(date) = (select year(enddate) from dates1)
          ${additionalWhere}
        ),
        cte_Actual AS (
          SELECT distinct RegionMarket,RPM RPM_Actual, VP VP_Actual, Property Property_Actual,bu,
          0 as WorkloadPerRPM_Actual
          FROM cte_Properties
        )
        SELECT RegionMarket,count(distinct RPM_Actual) RPM_Actual,count(distinct VP_Actual) VP_Actual,count(distinct bu) Property_Actual
        ,try_divide(count(distinct bu),count(distinct RPM_Actual)) as PropertyPerRPM_Actual,
         try_divide(count(distinct RPM_Actual),count(distinct VP_Actual)) as RPMPerVP_Actual
         FROM cte_Actual
        group by all
      `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getStaffingMarketKpi: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});
