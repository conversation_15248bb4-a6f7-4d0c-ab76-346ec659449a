const normalizeToArray = (value) => {
  if (!value) return null;
  return Array.isArray(value) ? value : [value];
};

const buildInClause = (field, values) => {
  if (!values || values.length === 0) return null;
  const escapedValues = values.map((v) => `'${String(v).replace(/'/g, "''")}'`);
  return `${field} IN (${escapedValues.join(", ")})`;
};

const getStaffingRpmAnalysisQuery = (year, month, department, businessType, marketleader, adminBu, region, market) => {
  // Build WHERE conditions for filtering
  const whereConditions = ["1=1"];

  // Normalize filter values to arrays
  const departments = normalizeToArray(department);
  const businessTypes = normalizeToArray(businessType);
  const marketleaders = normalizeToArray(marketleader);
  const adminBus = normalizeToArray(adminBu);
  const regions = normalizeToArray(region);
  const markets = normalizeToArray(market);

  const years = normalizeToArray(year);
  const months = normalizeToArray(month);

  const yearFilter = years && years.length > 0 ? years[0] : "2025";

  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  const maxSelectedMonth = months && months.length > 0 ? Math.max(...months.map((m) => parseInt(m))) : 2;
  const selectedMonth = parseInt(yearFilter) === currentYear ? Math.min(maxSelectedMonth, currentMonth) : maxSelectedMonth;

  const forecastStartMonth = selectedMonth + 1;
  const actualMonthRange = Array.from({ length: selectedMonth }, (_, i) => i + 1).join(",");

  const monthFilter = months && months.length > 0 ? months[months.length - 1] : "1";
  const endDateValue = `${yearFilter}-${String(monthFilter).padStart(2, "0")}-01`;

  // Build filter clauses
  if (departments && departments.length > 0) {
    const departmentClause = buildInClause("c.Department", departments);
    if (departmentClause) whereConditions.push(departmentClause);
  }

  if (businessTypes && businessTypes.length > 0) {
    const businessTypeClause = buildInClause("c.BusinessType", businessTypes);
    if (businessTypeClause) whereConditions.push(businessTypeClause);
  }

  if (marketleaders && marketleaders.length > 0) {
    const marketleaderClause = buildInClause("c.MarketLeader", marketleaders);
    if (marketleaderClause) whereConditions.push(marketleaderClause);
  }

  if (adminBus && adminBus.length > 0) {
    const propertyBus = adminBus
      .filter((bu) => bu !== null && bu !== undefined)
      .map((bu) => String(bu).split("-")[0])
      .filter(Boolean);
    if (propertyBus.length > 0) {
      const propertyBuClause = buildInClause("trim(c.AdminBU)", propertyBus);
      if (propertyBuClause) whereConditions.push(propertyBuClause);
    }
  }

  if (regions && regions.length > 0) {
    const regionClause = buildInClause("c.Region", regions);
    if (regionClause) whereConditions.push(regionClause);
  }

  if (markets && markets.length > 0) {
    const marketClause = buildInClause("c.Market", markets);
    if (marketClause) whereConditions.push(marketClause);
  }

  const whereClause = whereConditions.length > 1 ? `AND ${whereConditions.slice(1).join(" AND ")}` : "";

  return `
with 
dates1 as (Select last_day('${endDateValue}') as enddate),
cte_property as (select bu,a.adminbu,property,cast(BeginningOfOperations as date) as BeginningOfOperations
,region,rpm,vp,regionmarket
 From gold_Dev.edlh.dim_property a
 JOIN gold_dev.edlh.vw_income_statement_slicer c ON TRIM(a.AdminBu) = TRIM(c.AdminBU)
  where a.is_active=true and c.BusinessType='Property Management'  ${whereClause} 
 ),
cte_stabilised as  (select property_code,unit_code,unit_status,sStatus
,row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
,enddate
 from gold_dev.edlh.fact_property_unitscorecard a
 join cte_property b on trim(a.property_code)=trim(b.bu)
 cross join dates1 on 1=1 
  where
   dtstart<=(select enddate from dates1)
 ) ,
 cte_FYforecast AS (
         select b.rpm,sum(a.forecast) expectedPMFees from (
        SELECT a.propertyBu,ExpandTotal, total,
               try_divide(SUM(CASE WHEN MONTH(date) BETWEEN ${forecastStartMonth} AND 12 THEN forecast ELSE 0 END),cnt) +
               try_divide(SUM(CASE WHEN MONTH(date) IN (${actualMonthRange}) THEN actual ELSE 0 END),cnt) as forecast
         FROM gold_dev.edlh.fact_income_statement a
        LEFT JOIN gold_dev.edlh.vw_income_statement_slicer c ON TRIM(a.propertyBu) = TRIM(c.AdminBU)
        left join (select adminBu,count(distinct rpm) cnt from gold_Dev.edlh.dim_property where is_active=true group by all ) b on a.propertyBu=b.adminBu
        WHERE 1=1 AND YEAR(date)=${yearFilter} and c.businessType='Property Management' 
        and total='Property Management Fees' ${whereClause} 
        GROUP BY a.propertybu,expandtotal,total ,cnt) a 
        left join  (select distinct adminBu, rpm  from gold_Dev.edlh.dim_property where is_active=true  )b on a.propertyBu=b.adminbu
        group by all 
),
 cte_stage as (select property_code
 ,region,BeginningOfOperations,enddate
 ,rpm,vp,MONTHS_BETWEEN(enddate,BeginningOfOperations) year_diff
 ,(sum(case when unit_status!='Excluded' then 1 else 0 end)-sum(case when sstatus in
  ('Vacant Unrented Ready','Vacant Unrented Not Ready','Notice Unrented') then 1 else 0 end))*100 / count(*) occupancy
 From cte_stabilised a 
 left join cte_property b on trim(a.property_code)=trim(b.bu) 
 where rnk=1 group by all)
,cte_final as  ( 
select rpm,COALESCE(stabilized,0)stabilized,COALESCE(lease_up,0)lease_up,COALESCE(take_over,0)take_over from ( 
select property_code,rpm
,case when occupancy < 95 and year_diff/12<3 then 'Lease_Up' 
 when occupancy> 80 and year_diff<=6 then 'Take_Over' else 'Stabilized' end as Property_Strategy
from 
 cte_stage  ) pivot(count(property_code) for Property_Strategy in ('Stabilized','Lease_Up','Take_Over'))  
) 
select coalesce(a.RPM,'') RPM,SUM(coalesce(a.Stabilized,0)) Stabilised,SUM(coalesce(a.Lease_Up,0)) Lease_Up,
SUM(coalesce((a.lease_up+a.take_over+a.stabilized),0)) Total_Properties,round((SUM(coalesce(b.expectedPMFees,0))),0) Expected_PM_Fees
from cte_FYforecast b
left join cte_final a on a.rpm=b.rpm
Group By a.RPM

  `;
};

module.exports = {
  getStaffingRpmAnalysisQuery,
};
