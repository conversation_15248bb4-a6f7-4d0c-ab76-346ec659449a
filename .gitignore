# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json
*.mdc
TASKS.md
pids
*.pid
*.seed
*.pid.lock
lib-cov
coverage
CLAUDE.md
.nyc_output
.grunt
bower_components
.lock-wscript
build/Release
node_modules/
jspm_packages/
typings/
.npm
.eslintcache
.node_repl_history
.cursor/
.claude/ 
*.tgz
.yarn-integrity
.env
.env.test
.cache
.next
.nuxt
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
dist
out
bin
obj
appsettings.json
local.settings.json
__blobstorage__
__queuestorage__
__azurite_db*__.json
