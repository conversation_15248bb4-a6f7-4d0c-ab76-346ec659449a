trigger:
  branches:
    include:
      - 'dev'              

variables:                 
  azureSubscription: "Prism-SC"        
  appName: 'willowbridge-fn-dev'      
  vmImageName: 'ubuntu-latest'         
  nodeVersion: '20.x'                  
  

pool:                      
  vmImage: $(vmImageName)  

steps:                     

- task: UseNode@1
  inputs:
    version: '$(nodeVersion)'          
  displayName: 'Set Node.js version'

- script: |
    npm install                       
  displayName: 'Install dependencies'

- task: ArchiveFiles@2
  displayName: 'Archive function app source'
  inputs:
    rootFolderOrFile: '$(System.DefaultWorkingDirectory)'   
    includeRootFolder: false                                
    archiveFile: '$(Build.ArtifactStagingDirectory)/app.zip' 
    replaceExistingArchive: true                            

- task: AzureFunctionApp@2
  displayName: 'Deploy to Azure Function App'
  inputs:
    azureSubscription: '$(azureSubscription)'               
    appType: 'functionAppLinux'                             
    appName: '$(appName)'                                   
    package: '$(Build.ArtifactStagingDirectory)/app.zip'    
    runtimeStack: 'NODE|20'                                 
    deploymentMethod: 'zipDeploy'                           
